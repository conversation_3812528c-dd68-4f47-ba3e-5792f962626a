
<!DOCTYPE html>
<html>
  <head>
    <title>Secrets Of War - For the players, By the players</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="mobile-web-app-capable" content="yes">
    <!--
    <meta name="turbo" content="false">
    <meta name="turbo-prefetch" content="false"> 
    -->
    <meta name="csrf-param" content="authenticity_token" />
<meta name="csrf-token" content="fQqq9Wbk6i15CeWVXxguIZ_fBpMOzvxnbi_Ypr2pri-0GILq8FYExskt39Sz3DmAzc71v8Nv9hwTC5mFr6kOSA" />
    

    

    <link rel="manifest" href="/manifest.json">
    <link rel="icon" href="/favicon.png" type="image/png">
    <!--<link rel="icon" href="/icon.svg" type="image/svg+xml">-->
    <link rel="apple-touch-icon" href="/icon.png">
    <link rel="stylesheet" href="/assets/tailwind-3617fc2edad4e5ee321ef4de4a287042b409b803a56e9aa89d705c52609d1bd3.css" data-turbo-track="reload" />
<link rel="stylesheet" href="/assets/inter-font-8c3e82affb176f4bca9616b838d906343d1251adc8408efe02cf2b1e4fcf2bc4.css" data-turbo-track="reload" />
    <link rel="stylesheet" href="/assets/application-34cb2c6810c59a97a1aeaeb20ee70e935aed993eaf18d74c6b77287fc3d81e19.css" data-turbo-track="reload" />
    <script type="importmap" data-turbo-track="reload">{
  "imports": {
    "application": "/assets/application-3ff34e0404bb583c60f587c63a695f44d3b2549d5abf68c2fc6635ade4ebca6a.js",
    "@hotwired/turbo-rails": "/assets/turbo.min-c85b4c5406dd49df1f63e03a5b07120d39cc3e33bc2448f5e926b80514f9dfc8.js",
    "@hotwired/stimulus": "/assets/stimulus.min-dd364f16ec9504dfb72672295637a1c8838773b01c0b441bd41008124c407894.js",
    "@hotwired/stimulus-loading": "/assets/stimulus-loading-3576ce92b149ad5d6959438c6f291e2426c86df3b874c525b30faad51b0d96b3.js",
    "echarts": "/assets/echarts.min-090f9ae24ed9070985d2f30866838972a4b064121272ef2f4785c5aa9f51e4ca.js",
    "echarts/theme/dark": "/assets/echarts/theme/dark-5ad05e48c1b3e2b1e25811dacc8f12ae65e4f12c0623af52d3e39a082e0e62cc.js",
    "ahoy": "/assets/ahoy-ad2a2c1b794f17b560f4d20fa7d4397af448e41921273dc23bb18955082fc783.js",
    "sortablejs": "https://ga.jspm.io/npm:sortablejs@1.15.6/modular/sortable.esm.js",
    "controllers/application": "/assets/controllers/application-368d98631bccbf2349e0d4f8269afb3fe9625118341966de054759d96ea86c7e.js",
    "controllers/base_greeting_editor_controller": "/assets/controllers/base_greeting_editor_controller-aec3fe36e1fffe3bc67e08371a20350c5672b52377e1315f42602a6794b423d6.js",
    "controllers/chat_controller": "/assets/controllers/chat_controller-dcbd062bea0bf680e3e07bc090009becd265475ef9e1e63d1dd44876edb0c3c6.js",
    "controllers/clock_controller": "/assets/controllers/clock_controller-61f7bb5a9123f3b97f252e91f04ddc7da15d9e6a5ae4b570ce1c241b4ac3275a.js",
    "controllers/cloudflare_turnstile_controller": "/assets/controllers/cloudflare_turnstile_controller-af8c0251fdf1587399805b2eafc582d1f7e8c7d7366e7e65399908d464bedb20.js",
    "controllers/date_range_controller": "/assets/controllers/date_range_controller-faf16219ae74e051aaf9d797afb02781165312f1f7d185be3a9c5bcbb3b980d3.js",
    "controllers/deity_tribute_controller": "/assets/controllers/deity_tribute_controller-6bd97dfe632964118e381255983c7ffd474d6648a66d7c5b2919ffd30d6d56a0.js",
    "controllers/game_controller": "/assets/controllers/game_controller-0be36c58e7c623c2c6eba6558d0854314623387cbc05dcd9538bc07161b45001.js",
    "controllers": "/assets/controllers/index-31a9bee606cbc5cdb1593881f388bbf4c345bf693ea24e124f84b6d5c98ab648.js",
    "controllers/minimap_controller": "/assets/controllers/minimap_controller-175e90f847cd516456d68eeb8be50b9d02636ce70db6bb628cd5822f2244cbf0.js",
    "controllers/onboarding_create_player_controller": "/assets/controllers/onboarding_create_player_controller-bc224a2a58cc5c5c4f8f8c052f39ec1caa088d69ccc7e0199839e382beab0c3f.js",
    "controllers/pike_effect_controller": "/assets/controllers/pike_effect_controller-0d2fee36ba2a157741f4257ee7692fe14fad97bb7e58ff0113b1991902446bc4.js",
    "controllers/sortable_controller": "/assets/controllers/sortable_controller-a1e740e4ff84f95400fc65d6d3e137a1318f275a5070527db9049e1e025cc019.js",
    "controllers/tribute_preserve_controller": "/assets/controllers/tribute_preserve_controller-c387bde866c9e1312aa18dff7b10a1c9cf58906b074fd5f51f9ce4d6637e8472.js"
  }
}</script>
<link rel="modulepreload" href="/assets/application-3ff34e0404bb583c60f587c63a695f44d3b2549d5abf68c2fc6635ade4ebca6a.js">
<link rel="modulepreload" href="/assets/turbo.min-c85b4c5406dd49df1f63e03a5b07120d39cc3e33bc2448f5e926b80514f9dfc8.js">
<link rel="modulepreload" href="/assets/stimulus.min-dd364f16ec9504dfb72672295637a1c8838773b01c0b441bd41008124c407894.js">
<link rel="modulepreload" href="/assets/stimulus-loading-3576ce92b149ad5d6959438c6f291e2426c86df3b874c525b30faad51b0d96b3.js">
<link rel="modulepreload" href="/assets/echarts.min-090f9ae24ed9070985d2f30866838972a4b064121272ef2f4785c5aa9f51e4ca.js">
<link rel="modulepreload" href="/assets/echarts/theme/dark-5ad05e48c1b3e2b1e25811dacc8f12ae65e4f12c0623af52d3e39a082e0e62cc.js">
<link rel="modulepreload" href="/assets/ahoy-ad2a2c1b794f17b560f4d20fa7d4397af448e41921273dc23bb18955082fc783.js">
<link rel="modulepreload" href="https://ga.jspm.io/npm:sortablejs@1.15.6/modular/sortable.esm.js">
<link rel="modulepreload" href="/assets/controllers/application-368d98631bccbf2349e0d4f8269afb3fe9625118341966de054759d96ea86c7e.js">
<link rel="modulepreload" href="/assets/controllers/base_greeting_editor_controller-aec3fe36e1fffe3bc67e08371a20350c5672b52377e1315f42602a6794b423d6.js">
<link rel="modulepreload" href="/assets/controllers/chat_controller-dcbd062bea0bf680e3e07bc090009becd265475ef9e1e63d1dd44876edb0c3c6.js">
<link rel="modulepreload" href="/assets/controllers/clock_controller-61f7bb5a9123f3b97f252e91f04ddc7da15d9e6a5ae4b570ce1c241b4ac3275a.js">
<link rel="modulepreload" href="/assets/controllers/cloudflare_turnstile_controller-af8c0251fdf1587399805b2eafc582d1f7e8c7d7366e7e65399908d464bedb20.js">
<link rel="modulepreload" href="/assets/controllers/date_range_controller-faf16219ae74e051aaf9d797afb02781165312f1f7d185be3a9c5bcbb3b980d3.js">
<link rel="modulepreload" href="/assets/controllers/deity_tribute_controller-6bd97dfe632964118e381255983c7ffd474d6648a66d7c5b2919ffd30d6d56a0.js">
<link rel="modulepreload" href="/assets/controllers/game_controller-0be36c58e7c623c2c6eba6558d0854314623387cbc05dcd9538bc07161b45001.js">
<link rel="modulepreload" href="/assets/controllers/index-31a9bee606cbc5cdb1593881f388bbf4c345bf693ea24e124f84b6d5c98ab648.js">
<link rel="modulepreload" href="/assets/controllers/minimap_controller-175e90f847cd516456d68eeb8be50b9d02636ce70db6bb628cd5822f2244cbf0.js">
<link rel="modulepreload" href="/assets/controllers/onboarding_create_player_controller-bc224a2a58cc5c5c4f8f8c052f39ec1caa088d69ccc7e0199839e382beab0c3f.js">
<link rel="modulepreload" href="/assets/controllers/pike_effect_controller-0d2fee36ba2a157741f4257ee7692fe14fad97bb7e58ff0113b1991902446bc4.js">
<link rel="modulepreload" href="/assets/controllers/sortable_controller-a1e740e4ff84f95400fc65d6d3e137a1318f275a5070527db9049e1e025cc019.js">
<link rel="modulepreload" href="/assets/controllers/tribute_preserve_controller-c387bde866c9e1312aa18dff7b10a1c9cf58906b074fd5f51f9ce4d6637e8472.js">
<script type="module">import "application"</script>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.16.0/cdn/themes/light.css" />
    <script type="module" src="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.16.0/cdn/shoelace-autoloader.js"></script>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Beiruti:wght@200..900&family=LXGW+WenKai+Mono+TC:wght@300;400;700&display=swap" rel="stylesheet">
  </head>
  <body
    class="theme-classic template-game"
    data-controller="game pike-effect"
    data-game-target="body"
    data-theme="classic"
    data-action="keydown->game#keyEvent"
    data-current-time="1750684704"
  >
      <turbo-cable-stream-source channel="Turbo::StreamsChannel" signed-stream-name="ImdhbWVfcGxheWVyOjE4OSI=--4371d94291fdd74090e9eeb7a6a3b3793e46efd2814488d393d8c9ed4890771a"></turbo-cable-stream-source>
      <div id="game_effects_container" data-pike-effect-target="container"></div>
    
    <turbo-frame id="game" data-turbo-action="advance" data-game-target="gameFrame">
      <div class="body w-[720px] md:w-[780px] lg:w-[980px] xl:w-[1200px] 2xl:w-[1500px] mx-auto h-full overflow-hidden">
        <div class="mx-auto">
          <div class="flex gap-3">
            <div class="w-3/12 lg:w-2/12 flex-none space-y-3">
              <a href="/game">
                <img src="/theme/classic/logo.jpg" width="220" height="70" title="Secrets of War! For the players, by the players!" class="rounded mx-auto">
</a>              
      <div class="info-box bg-gray-300 text-sm overflow-hidden rounded ">
        <div class="bg-zinc-700 font-bold text-white px-2 py-1 text-sm flex space-x-2">
          <div class="flex-1">Fame</div>
        </div>
      <div class="px-1 py-2">
        
  <div class="text-center text-xs md:text-sm text-right font-mono">

    <a data-turbo="false" href="/toplists">
      <div class=" items-center mb-2 px-2 py-1 flex flex-cols-2 gap-2 bg-white rounded-lg hover:bg-gray-200  font-bold">
        <sl-icon name="trophy-fill" class="text-blue-700 m-1 text-xl" style="margin-left:3px;"></sl-icon>
        <div>Rank</div>
        <div class="flex-1 h-full">34</div>
      </div>
</a>
    <div class=" items-center mb-2 px-2 py-1 flex flex-cols-2 gap-2 bg-white rounded-lg font-bold">
      <sl-icon name="star-fill" class="text-blue-700 m-1 text-xl" style="margin-left:3px;"></sl-icon>
      <div>Power(B)</div>
      <div class="flex-1 h-full">399,791</div>
    </div>

    <a data-turbo-frame="playCenter" class="" data-method="get" href="/game/play/shop/unit_shop?tech_category=attack_tech&amp;unit_category=attack_unit">
      <div class=" items-center mb-2 px-2 py-1 flex flex-wrap flex-cols-2 gap-x-2 bg-white hover:bg-gray-200 rounded-lg font-bold">
        <sl-icon name="diagram-3-fill" class="text-blue-700 m-1 text-xl" style="margin-left:3px;"></sl-icon>
        <div>Strength</div>
        <div class="flex-1 h-full">90,901</div>
        <div class="text-xs w-full text-right text-blue-500 hover:text-blue-700 hover:underline">Upgrade</div>
      </div>
</a>
    <a data-turbo-frame="playCenter" class="" data-method="get" href="/game/play/shop/unit_shop?tech_category=def_tech&amp;unit_category=defense_unit">
      <div class=" items-center mb-2 px-2 py-1 flex flex-wrap flex-cols-2 gap-x-2 bg-white hover:bg-gray-200 rounded-lg font-bold">
        <sl-icon name="shield-fill" class="text-blue-700 m-1 text-xl" style="margin-left:3px;"></sl-icon>
        <div>Defense</div>
        <div class="flex-1 h-full">86,662</div>
        <div class="text-xs w-full text-right text-blue-500 hover:text-blue-700 hover:underline">Upgrade</div>
      </div>
</a>
    <div class=" items-center mb-2 px-2 py-1 flex flex-cols-2 gap-2 bg-white rounded-lg font-bold">
      <sl-icon name="geo-alt-fill" class="text-blue-700 m-1 text-xl" style="margin-left:3px;"></sl-icon>
      <div>Base</div>
      <div class="flex-1 h-full">
        81, 73
      </div>
    </div>

    <div class=" items-center mb-2 px-2 py-1 flex flex-cols-2 gap-2 bg-white rounded-lg font-bold">
      <sl-icon name="house-gear-fill" class="text-blue-700 m-1 text-xl" style="margin-left:3px;"></sl-icon>
      <div>Factories</div>
      <div class="flex-1 h-full">
        6
      </div>
    </div>

    <div class=" items-center mb-2 px-2 py-1 flex flex-cols-2 gap-2 bg-white rounded-lg font-bold">
      <sl-icon name="cart-fill" class="text-blue-700 m-1 text-xl" style="margin-left:3px;"></sl-icon>
      <div>Inventory</div>
      <div class="flex-1 h-full">
        5 / 1950
      </div>
    </div>

    <div class=" items-center mb-2 px-2 py-1 flex flex-cols-2 gap-2 bg-white rounded-lg font-bold">
      <sl-icon name="clock-fill" class="text-blue-700 m-1 text-xl" style="margin-left:3px;"></sl-icon>
      <div>Time</div>
      <div class="flex-1 h-full" data-controller="clock" data-clock-target="gameTimer" data-current-time="1750684704" data-format="DD/MM-YY HH:mm:ss" data-turbo-permanent="true">
        23/06-25 13:18:24
      </div>
    </div>
  </div>

      </div>
    </div>

      <div class="info-box bg-gray-300 text-sm overflow-hidden rounded ">
        <div class="bg-zinc-700 font-bold text-white px-2 py-1 text-sm flex space-x-2">
          <div class="flex-1">Battle Log</div>
        </div>
      <div class="px-1 py-2">
        
  <div class="text-center text-xs text-right font-mono">
    <a data-turbo-action="advance" data-turbo-frame="playCenter" href="/game/battle_history/history?type=attack">
      <div class="resource mb-2 bg-white rounded-lg p-2 hover:cursor-pointer hover:bg-gray-200">
        <div class="flex items-center gap-2">
          <div>Attack Logs</div>
          <div class="flex-1">137</div>
          <div class="text-xs text-right text-blue-500 hover:text-blue-700 hover:underline">Open</div>
        </div>
      </div>
</a>    <a data-turbo-action="advance" data-turbo-frame="playCenter" href="/game/battle_history/history?type=defense">
      <div class="resource mb-2 bg-white rounded-lg p-2 hover:cursor-pointer hover:bg-gray-200">
        <div class="flex items-center gap-2">
          <div>Defense Logs</div>
          <div class="flex-1">12</div>
          <div class="text-xs text-right text-blue-500 hover:text-blue-700 hover:underline">Open</div>
        </div>
      </div>
</a>    <a data-turbo-action="advance" data-turbo-frame="playCenter" href="/game/battle_history/history?type=pike">
      <div class="resource mb-2 bg-white rounded-lg p-2 hover:cursor-pointer hover:bg-gray-200">
        <div class="flex items-center gap-2">
          <div>Pike Logs</div>
          <div class="flex-1">73</div>
          <div class="text-xs text-right text-blue-500 hover:text-blue-700 hover:underline">Open</div>
        </div>
      </div>
</a>      <div class="resource mb-2 bg-white rounded-lg p-2 hover:cursor-pointer hover:bg-gray-200">
        <div class="flex items-center gap-2">
          <div>Land Mines</div>
          <div class="flex-1">0</div>
        </div>
      </div>
  </div>

      </div>
    </div>


            </div>
            <div class="main flex-grow max-w-[calc(100%-2rem)] overflow-auto">
                    <div class="navigation h-14 relative mb-4 rounded rounded-lg">
        <div class="grid grid-cols-3 md:grid-cols-5 font-sans text-center text-white abs" style="z-index: 10;">
          <a class="p-2 md:p-4 text-sm md:text-base hover:font-bold hover:cursor-pointer" href="/game">
            <!-- <span>Status</span> -->
            <!-- <sl-badge variant="primary" pill pulse class="-mt-2 ml-1 absolute">1</sl-badge> -->
</a>          <a class="p-2 md:p-4 text-sm md:text-base hover:font-bold hover:cursor-pointer" href="/game/play">
            <!-- <span>World</span> -->
            <!-- <sl-badge variant="neutral" pill pulse class="-mt-2 ml-1 absolute">1</sl-badge> -->
            <!-- <div class="-mt-7 ml-16">
              <sl-badge variant="success" pill pulse>New</sl-badge>
            </div>-->
</a>          <a class="p-2 md:p-4 text-sm md:text-base hover:font-bold hover:cursor-pointer trapezoid" href="/game/play">
            <span>Play</span>
</a>          <a class="p-2 md:p-4 text-sm md:text-base hover:font-bold hover:cursor-pointer" href="/game/play">
            <!-- <span>Forums</span> -->
            <!-- <sl-badge variant="danger" pill pulse class="-mt-2 ml-1 absolute">1</sl-badge> -->
</a>          <a class="p-2 md:p-4 text-sm md:text-base hover:font-bold hover:cursor-pointer" href="/game/play">
            <!-- <span>Forums</span> -->
            <!-- <sl-badge variant="danger" pill pulse class="-mt-2 ml-1 absolute">1</sl-badge> -->
</a>        </div>
        <div class="absolute top-0 left-0 w-full h-full bg-black bg-opacity-50 rounded rounded-lg rounded-t-none" style="z-index: -1;"></div>
      </div>

              
              
              <turbo-frame id="playCenter">
                  <div class="content">
                    

<div class="overflow-hidden">
  <div class="bg-slate-700 text-white rounded mx-auto">
      <div class="relative overflow-hidden max-h-[900px] flex items-center justify-center">
    <div class="absolute inset-0 opacity-25">
      <img class="w-full h-full object-cover" src="/theme/classic/images/game/energy_field/day/energyfield_day.jpg" />
    </div>
    <img class="relative z-10 w-full h-full lg:w-[600px]" src="/theme/classic/images/game/energy_field/day/energyfield_day.jpg" />
    <div class="bg-slate-700 text-xl mt-4 text-white px-6 py-2 uppercase beiruti-500" style="position: absolute; top: 0; z-index: 20;">
      Geothermal Vents
    </div>
  </div>
  <div class="py-8 text-center">
    This is the place for you to gather more energy. The more energy, the more units in your army!!!

      <div class="flex flex-wrap justify-center gap-2">
          <form action="/game/play" accept-charset="UTF-8" method="post"><input type="hidden" name="authenticity_token" value="RyuROxaah9_iKEeyqtfmJn5YVfOaPlUBfxRpLZYC95aQK7Fdav-wefx-0BYMK5Bt2glXg0Ae_AHszfiizjrksQ" autocomplete="off" />
            <input value="gather" autocomplete="off" type="hidden" name="player_action" id="player_action" />
            <input value="16826" autocomplete="off" type="hidden" name="player_location" id="player_location" />
            <input type="submit" name="commit" value="Gather Energy (G)" class="text-white hover:underline bg-slate-700 border-none p-0 cursor-pointer" data-game-target="responsiveKey" data-keymap="KeyG" data-disable-with="Gather Energy (G)" />
</form>      </div>
  </div>

<turbo-cable-stream-source channel="Turbo::StreamsChannel" signed-stream-name="IloybGtPaTh2YzI5M1oyRnRaUzlIWVcxbFRXRndVRzlwYm5Rdk1UWTRNalk6WjJsa09pOHZjMjkzWjJGdFpTOUhZVzFsVUd4aGVXVnlMekU0T1E6cGxheWVyX2xpc3Qi--0448944e1b0e731fbadb4ac731cf6ccafd700949ee156e668d827557ce0df706"></turbo-cable-stream-source>
<div id="player_list">
</div>
  </div>
</div>

                  </div>
              </turbo-frame>
            </div>
            <div class="w-2/12 flex-none space-y-3">
              <div class="navigation h-14 relative mb-4 rounded rounded-lg">
                <div class="grid grid-flow-col auto-cols-fr font-sans text-center text-white abs" style="z-index: 10;">

                  <sl-tooltip content="Account Settings" placement="bottom">
                    <a class="pt-4 text-xl items-center justify-center h-full text-zinc-400 hover:font-bold hover:text-zinc-50" href="/game/account">
                      <sl-icon name="gear-fill"></sl-icon>
</a>                  </sl-tooltip>


                  <sl-tooltip content="Discord" placement="bottom">
                    <a class="pt-4 text-xl items-center justify-center h-full text-zinc-400 hover:font-bold hover:text-zinc-50" target="_blank" href="https://discord.gg/7nbGZ5FGnp">
                      <sl-icon name="discord"></sl-icon>
</a>                  </sl-tooltip>
                  
                  <sl-tooltip content="Log Out" placement="bottom">
                    <form class="button_to" method="post" action="/users/logout"><input type="hidden" name="_method" value="delete" autocomplete="off" /><button data-turbo="false" class="pt-4 text-xl hover:font-bold text-red-700 hover:text-red-500" title="Log Out" type="submit">
                      <sl-icon name="x-octagon-fill"></sl-icon>
</button><input type="hidden" name="authenticity_token" value="-6ij0-_6Ul_bT_QOzE-BFhGjrjqSD9ilTWqTfFbYEhsoaqRYcShGDfWNAnIjiuiQ5r-e7zmjzKbQ7gyb9evgZA" autocomplete="off" /></form>                  </sl-tooltip>
                </div>
                <div class="absolute top-0 left-0 w-full h-full bg-black bg-opacity-50 rounded rounded-lg rounded-t-none" style="z-index: -1;"></div>
              </div>
              
      <div class="info-box bg-gray-300 text-sm overflow-hidden rounded ">
        <div class="bg-zinc-700 font-bold text-white px-2 py-1 text-sm flex space-x-2">
          <div class="flex-1"><div class='flex flex-cols-2'><div class='flex-1'>P3X-235</div><div class='text-right'>113, 26</div></div></div>
        </div>
      <div class="px-1 py-2">
        
  <div class="grid grid-cols-3 w-[123px] mx-auto">
          <form action="/game/play" accept-charset="UTF-8" method="post"><input type="hidden" name="authenticity_token" value="7lYY5_kAkXEcy9JNClBHAH-VGVOW0b4-FSsl4TXYU385VjiBhWWm1wKdRemsrDFL28QbI0zxFz6G8rRubeBAWA" autocomplete="off" />
            <input type="hidden" name="player_action" id="player_action" value="move" autocomplete="off" />
            <input type="hidden" name="direction" id="direction" value="nw" autocomplete="off" />
            <button type="submit" class="block hover:cursor-pointer" data-game-target="responsiveKey", data-keymap="ArrowUpLeft,Numpad7,KeyQ">
              <img src="/theme/classic/images/interface/movement/nw.gif" alt="Travel north west">
            </button>
</form>          <form action="/game/play" accept-charset="UTF-8" method="post"><input type="hidden" name="authenticity_token" value="Vq-eUWw3YTHzbOntBlnD5sPSEmSVUtd95Wj-4st6YHuBr743EFJWl-06fkmgpbWtZ4MQFE9yfn12sW9tk0JzXA" autocomplete="off" />
            <input type="hidden" name="player_action" id="player_action" value="move" autocomplete="off" />
            <input type="hidden" name="direction" id="direction" value="n" autocomplete="off" />
            <button type="submit" class="block hover:cursor-pointer" data-game-target="responsiveKey", data-keymap="ArrowUp,Numpad8,KeyW">
              <img src="/theme/classic/images/interface/movement/n.gif" alt="Travel north">
            </button>
</form>          <form action="/game/play" accept-charset="UTF-8" method="post"><input type="hidden" name="authenticity_token" value="eZMH1i3QX5CZwrqXB-Iyn9ppdiA4EgSj9p-Ab7oZ_4OukyewUbVoNoeULTOhHkTUfjh0UOIyraNlRhHg4iHspA" autocomplete="off" />
            <input type="hidden" name="player_action" id="player_action" value="move" autocomplete="off" />
            <input type="hidden" name="direction" id="direction" value="ne" autocomplete="off" />
            <button type="submit" class="block hover:cursor-pointer" data-game-target="responsiveKey", data-keymap="ArrowUpRight,Numpad9,KeyE">
              <img src="/theme/classic/images/interface/movement/ne.gif" alt="Travel north east">
            </button>
</form>          <form action="/game/play" accept-charset="UTF-8" method="post"><input type="hidden" name="authenticity_token" value="opH31x76mr98Nd2BWhKS1E6jIBEprq9wl414IIlq_wd1kdexYp-tGWJjSiX87uSf6vIiYfOOBnAEVOmv0VLsIA" autocomplete="off" />
            <input type="hidden" name="player_action" id="player_action" value="move" autocomplete="off" />
            <input type="hidden" name="direction" id="direction" value="w" autocomplete="off" />
            <button type="submit" class="block hover:cursor-pointer" data-game-target="responsiveKey", data-keymap="ArrowLeft,Numpad4,KeyA">
              <img src="/theme/classic/images/interface/movement/w.gif" alt="Travel west">
            </button>
</form>          <form action="/game/play" accept-charset="UTF-8" method="post"><input type="hidden" name="authenticity_token" value="blxaQYp3sJAl-S_K13Y-Apn6XTdr-NU3w8pEQXxKdNa5XHon9hKHNjuvuG5xikhJPatfR7HYfDdQE9XOJHJn8Q" autocomplete="off" />
            <input type="hidden" name="player_action" id="player_action" value="move" autocomplete="off" />
            <input type="hidden" name="direction" id="direction" value="home" autocomplete="off" />
            <button type="submit" class="block hover:cursor-pointer" data-game-target="responsiveKey", data-keymap="Numpad5,KeyS">
              <img src="/theme/classic/images/interface/movement/home.gif" alt="Refresh">
            </button>
</form>          <form action="/game/play" accept-charset="UTF-8" method="post"><input type="hidden" name="authenticity_token" value="28G8-PDeXVFNFOsIsmqqPoKaAkDJz4hMvKCzDv9qgAwMwZyejLtq91NCfKwUltx1JssAMBPvIUwveSKBp1KTKw" autocomplete="off" />
            <input type="hidden" name="player_action" id="player_action" value="move" autocomplete="off" />
            <input type="hidden" name="direction" id="direction" value="e" autocomplete="off" />
            <button type="submit" class="block hover:cursor-pointer" data-game-target="responsiveKey", data-keymap="ArrowRight,Numpad6,KeyD">
              <img src="/theme/classic/images/interface/movement/e.gif" alt="Travel east">
            </button>
</form>          <form action="/game/play" accept-charset="UTF-8" method="post"><input type="hidden" name="authenticity_token" value="kHwsEWemo-RxHeT5Ipg-627I3pVZ5N2-3jSQ0YdcFeNHfAx3G8OUQm9Lc12EZEigypnc5YPEdL5N7QFe32QGxA" autocomplete="off" />
            <input type="hidden" name="player_action" id="player_action" value="move" autocomplete="off" />
            <input type="hidden" name="direction" id="direction" value="sw" autocomplete="off" />
            <button type="submit" class="block hover:cursor-pointer" data-game-target="responsiveKey", data-keymap="ArrowDownLeft,Numpad1,KeyZ">
              <img src="/theme/classic/images/interface/movement/sw.gif" alt="Travel south west">
            </button>
</form>          <form action="/game/play" accept-charset="UTF-8" method="post"><input type="hidden" name="authenticity_token" value="8donpgNNTPwofds7pV_vM7ZEX3BFxXM3X3OpXzxhHqIm2gfAfyh7WjYrTJ8Do5l4EhVdAJ_l2jfMqjjQZFkNhQ" autocomplete="off" />
            <input type="hidden" name="player_action" id="player_action" value="move" autocomplete="off" />
            <input type="hidden" name="direction" id="direction" value="s" autocomplete="off" />
            <button type="submit" class="block hover:cursor-pointer" data-game-target="responsiveKey", data-keymap="ArrowDown,Numpad2,KeyX">
              <img src="/theme/classic/images/interface/movement/s.gif" alt="Travel south">
            </button>
</form>          <form action="/game/play" accept-charset="UTF-8" method="post"><input type="hidden" name="authenticity_token" value="uPuCWvCBVNQK8jruOWwnem6cUqKxJFLUnzbxbQwjDINv-6I8jORjchSkrUqfkFExys1Q0msE-9QM72DiVBsfpA" autocomplete="off" />
            <input type="hidden" name="player_action" id="player_action" value="move" autocomplete="off" />
            <input type="hidden" name="direction" id="direction" value="se" autocomplete="off" />
            <button type="submit" class="block hover:cursor-pointer" data-game-target="responsiveKey", data-keymap="ArrowDownRight,Numpad3,KeyC">
              <img src="/theme/classic/images/interface/movement/se.gif" alt="Travel south east">
            </button>
</form>  </div>

    <div class="pt-4">
      <form action="/game/play" accept-charset="UTF-8" method="post"><input type="hidden" name="authenticity_token" value="sKGIJKqSI88hHjBa1uCNRzKqH1-wJgCxO8xOyhgEGihnoahC1vcUaT9Ip_5wHPsMlvsdL2oGqbGoFd9FQDwJDw" autocomplete="off" />
        <input type="hidden" name="player_action" id="player_action" value="toggle_troop_transport" autocomplete="off" />
          <button name="button" type="submit" class="block text-blue-500 mx-auto hover:underline hover:cursor-pointer" data-game-target="responsiveKey" data-keymap="KeyT" title="Enable Troop Transport">Enable Troop Transport (T)</button>
</form>    </div>

      </div>
    </div>

      <div class="info-box bg-gray-300 text-sm overflow-hidden rounded ">
        <div class="bg-zinc-700 font-bold text-white px-2 py-1 text-sm flex space-x-2">
          <div class="flex-1">Resource Panel</div>
        </div>
      <div class="px-1 py-2">
        
  <div class="text-center text-xs text-right font-mono">
    <div class="resource mb-2 bg-white rounded p-1 hover:cursor-pointer hover:bg-gray-200">
      <div class="flex items-center gap-2">
        <div>Metal</div>
        <div class="flex-1">13,969</div>
      </div>
      <div class="text-sm text-gray-600">
        Banked: 0
      </div>
    </div>
    <div class="resource mb-2 bg-white rounded p-1 hover:cursor-pointer hover:bg-gray-200">
      <div class="flex items-center gap-2">
        <div>Energy</div>
        <div class="flex-1">24,489</div>
      </div>
      <div class="text-sm text-gray-600">
        Banked: 0
      </div>
    </div>
  </div>

      </div>
    </div>

      <div class="info-box bg-gray-300 text-sm overflow-hidden rounded ">
        <div class="bg-zinc-700 font-bold text-white px-2 py-1 text-sm flex space-x-2">
          <div class="flex-1">Shrine Buffs</div>
        </div>
      <div class="px-1 py-2">
        
  <div class="text-center text-xs text-right font-mono">
    <sl-tooltip content="Gathering Buffs" placement="left">
      <div class="resource mb-2 bg-white rounded p-1 hover:cursor-pointer hover:bg-gray-200">
            <div class="flex items-center mb-2 mx-4 rounded animate-pulse bg-red-200">
            <sl-icon name="suit-spade-fill" class="text-yellow-600 m-1 text-xl" style="margin-left:3px;"></sl-icon>No buff active
          </div>
            <div class="flex items-center mb-2 mx-4 rounded animate-pulse bg-red-200">
            <sl-icon name="suit-heart-fill" class="text-yellow-600 m-1 text-xl" style="margin-left:3px;"></sl-icon>No buff active
          </div>
            <div class="flex items-center mb-2 mx-4 rounded animate-pulse bg-red-200">
            <sl-icon name="suit-diamond-fill" class="text-yellow-600 m-1 text-xl" style="margin-left:3px;"></sl-icon>No buff active
          </div>
            <div class="flex items-center mb-2 mx-4 rounded animate-pulse bg-red-200">
            <sl-icon name="suit-club-fill" class="text-yellow-600 m-1 text-xl" style="margin-left:3px;"></sl-icon>No buff active
          </div>
      </div>
    </sl-tooltip>

    <sl-tooltip content="Bonus" placement="left">
      <div class="resource mb-2 bg-white rounded p-1 hover:cursor-pointer hover:bg-gray-200">
        <div class="flex items-center gap-2">
          <div>Total Bonus</div>
          <div class="flex-1">x 1.0</div>
        </div>
      </div>
    </sl-tooltip>
  </div>

      </div>
    </div>


              
              <!-- Pike Effect Tester Widget (Development/Admin Only) -->
              
            </div>
          </div>
        </div>
      </div>
    </turbo-frame>
    
    
  <script defer src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"rayId":"9544456abce655fa","version":"2025.6.2","r":1,"token":"db969d74e21641a3b0291350dbdab5bf","serverTiming":{"name":{"cfExtPri":true,"cfEdge":true,"cfOrigin":true,"cfL4":true,"cfSpeedBrain":true,"cfCacheStatus":true}}}' crossorigin="anonymous"></script>
</body>
</html>