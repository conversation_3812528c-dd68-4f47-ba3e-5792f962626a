{"level":"info","message":"Already logged in.","timestamp":"07:20:28"}
{"dbPath":"./data/data.sqlite3","hasPage":true,"level":"debug","message":"injectSidebar called with page and dbPath:","timestamp":"07:20:28"}
{"level":"info","message":"Injecting sidebar overlay...","timestamp":"07:20:28"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Sidebar overlay script injected.\u001b[22m\u001b[39m","timestamp":"07:20:28"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4,\"time\":0},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":31896,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spade\":false,\"heart\":false,\"diamond\":false,\"club\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"You know what they say... Not even 1,466 Joules of Energy can buy happiness... but it sure does make you feel good!\",\"energy\":0},\"movement\":{\"0\":{\"direction\":\"nw\",\"x\":47,\"y\":120},\"1\":{\"direction\":\"n\",\"x\":47,\"y\":120},\"2\":{\"direction\":\"ne\",\"x\":47,\"y\":120},\"3\":{\"direction\":\"w\",\"x\":47,\"y\":120},\"4\":{\"direction\":\"home\",\"x\":47,\"y\":120},\"5\":{\"direction\":\"e\",\"x\":47,\"y\":120},\"6\":{\"direction\":\"sw\",\"x\":47,\"y\":120},\"7\":{\"direction\":\"s\",\"x\":47,\"y\":120},\"8\":{\"direction\":\"se\",\"x\":47,\"y\":120}},\"currentCoords\":\"47, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"06:57 am\"}","id":71,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:20:28","updated_at":"2025-06-24 10:57:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:20:28"}
{"level":"error","message":"Error updating sidebar: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at updateSidebar (D:\\BRAIN\\automation\\scrape.ts:456:18)\n    at injectSidebar (D:\\BRAIN\\automation\\scrape.ts:476:3)\n    at main (D:\\BRAIN\\automation\\login.ts:63:5)","timestamp":"07:20:28"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:20:28"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at main (D:\\BRAIN\\automation\\login.ts:66:5)","timestamp":"07:20:28"}
{"battleLog":{"attack":137,"defense":12,"land_mines":0,"pike":74},"currentCoords":"47, 120","date":"6-24-2025","inventory":{"capacity":1950,"current":11},"level":"info","message":"[SCRAPE] Scraped game data:","resourcePanel":{"energy":33362,"energy_banked":0,"metal":24858,"metal_banked":0},"shrineBuffs":{"clubs":false,"diamonds":false,"hearts":false,"spades":false,"totalBonus":1},"stats":{"base":"81, 73","defense":86662,"factories":4,"power":399730,"rank":34,"strength":90884},"tile":{"energy":0,"message":"This is the place for you to gather more energy. The more energy, the more units in your army!!!","title":"Geothermal Vents"},"time":"07:20 am","timestamp":"07:20:28","troopTransport":false}
{"level":"info","message":"Bot is running. Browser will remain open.","timestamp":"07:20:28"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:20:28"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:20:28"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:491:7","timestamp":"07:20:28"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:20:28"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"This is the place for you to gather more energy. The more energy, the more units in your army!!!\",\"energy\":0},\"currentCoords\":\"47, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:20 am\"}","id":72,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:20:29","updated_at":"2025-06-24 11:20:28"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:20:29"}
{"level":"error","message":"Error updating sidebar: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at Timeout.updateSidebar (D:\\BRAIN\\automation\\scrape.ts:456:18)","timestamp":"07:20:29"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:20:42"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:20:42"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:20:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:20:45"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:20:50"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:20:50"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:20:53"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:20:53"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:20:53"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:20:53"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:491:7","timestamp":"07:20:53"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:20:53"}
{"level":"info","message":"Already logged in.","timestamp":"07:23:11"}
{"dbPath":"./data/data.sqlite3","hasPage":true,"level":"debug","message":"injectSidebar called with page and dbPath:","timestamp":"07:23:11"}
{"level":"info","message":"Injecting sidebar overlay...","timestamp":"07:23:11"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Sidebar overlay script injected.\u001b[22m\u001b[39m","timestamp":"07:23:11"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:20 am\"}","id":74,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:11","updated_at":"2025-06-24 11:20:53"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:11"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:23:11"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:23:12"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at main (D:\\BRAIN\\automation\\login.ts:66:5)","timestamp":"07:23:12"}
{"battleLog":{"attack":137,"defense":12,"land_mines":0,"pike":74},"currentCoords":"51, 120","date":"6-24-2025","inventory":{"capacity":1950,"current":11},"level":"info","message":"[SCRAPE] Scraped game data:","resourcePanel":{"energy":33362,"energy_banked":0,"metal":24858,"metal_banked":0},"shrineBuffs":{"clubs":false,"diamonds":false,"hearts":false,"spades":false,"totalBonus":1},"stats":{"base":"81, 73","defense":86662,"factories":4,"power":399730,"rank":34,"strength":90884},"tile":{"energy":0,"message":"Need a new pike? Go get some Metal for it! This is the place for gathering that.","title":"Metal Deposit"},"time":"07:23 am","timestamp":"07:23:12","troopTransport":false}
{"level":"info","message":"Bot is running. Browser will remain open.","timestamp":"07:23:12"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:23:12"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:12"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:23:12"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:12","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:12"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:12","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:12"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:13","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:13"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:13","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:13"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:14","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:14"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:14","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:14"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:23:15"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:23:15"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:15"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:15","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:15"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:16","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:16"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:16","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:16"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:23:16"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:23:16"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:16"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:17","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:17"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:17","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:17"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:18","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:18"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:23:18"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:23:18"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:18"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:18","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:18"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:19","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:19"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:19","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:19"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:23:19"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:23:19"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:19"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:20","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:20"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:20","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:20"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:21","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:21"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:23:21"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:23:21"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:21"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:21","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:21"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:22","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:22"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:22","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:22"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:23:22"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:23:22"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:22"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:23","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:23"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:23","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:23"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:23:24"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:24","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:24"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:23:24"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:24"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"51, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":76,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:24","updated_at":"2025-06-24 11:23:12"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:24"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:23:24"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:23:24"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:23:24"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:23:24"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:24"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:23:24"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"This is the place for you to gather more energy. The more energy, the more units in your army!!!\",\"energy\":0},\"currentCoords\":\"52, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":77,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:25","updated_at":"2025-06-24 11:23:24"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:25"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:23:25"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:23:25"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"This is the place for you to gather more energy. The more energy, the more units in your army!!!\",\"energy\":0},\"currentCoords\":\"52, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":77,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:25","updated_at":"2025-06-24 11:23:24"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:25"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:23:25"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:25"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:23:25"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:25"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:23:25"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Dizzy? Energy makes the world go 'round. With the 1,239 Joules of Energy you just gathered, it's no wonder you are losing your balance!\",\"energy\":0},\"currentCoords\":\"52, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":78,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:26","updated_at":"2025-06-24 11:23:25"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:26"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Dizzy? Energy makes the world go 'round. With the 1,239 Joules of Energy you just gathered, it's no wonder you are losing your balance!\",\"energy\":0},\"currentCoords\":\"52, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":78,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:26","updated_at":"2025-06-24 11:23:25"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:26"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:23:27"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:23:27"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Dizzy? Energy makes the world go 'round. With the 1,239 Joules of Energy you just gathered, it's no wonder you are losing your balance!\",\"energy\":0},\"currentCoords\":\"52, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":78,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:27","updated_at":"2025-06-24 11:23:25"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:27"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:27"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Dizzy? Energy makes the world go 'round. With the 1,239 Joules of Energy you just gathered, it's no wonder you are losing your balance!\",\"energy\":0},\"currentCoords\":\"52, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":78,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:27","updated_at":"2025-06-24 11:23:25"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:27"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:23:27"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:23:27"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Dizzy? Energy makes the world go 'round. With the 1,239 Joules of Energy you just gathered, it's no wonder you are losing your balance!\",\"energy\":0},\"currentCoords\":\"52, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":78,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:28","updated_at":"2025-06-24 11:23:25"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:28"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:23:28"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:23:28"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":33362,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Dizzy? Energy makes the world go 'round. With the 1,239 Joules of Energy you just gathered, it's no wonder you are losing your balance!\",\"energy\":0},\"currentCoords\":\"52, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":78,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:28","updated_at":"2025-06-24 11:23:25"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:28"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:23:28"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:23:28"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:28"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:23:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:23:29"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:23:29"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:29","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:29"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:29","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:29"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"07:23:30"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"07:23:30"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:30","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:30"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"07:23:30"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:30","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:30"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:31","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:31"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"07:23:31"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"07:23:31"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:31","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:31"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"07:23:31"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:32","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:32"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:32","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:32"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"07:23:33"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"07:23:33"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:33","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:33"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"07:23:33"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:33","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:33"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:34","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:34"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"07:23:34"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"07:23:34"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:34","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:34"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"07:23:34"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:35","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:35"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:35","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:35"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"07:23:36"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"07:23:36"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:36","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:36"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"07:23:36"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:36","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:36"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:37","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:37"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"07:23:37"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"07:23:37"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:37","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:37"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"07:23:37"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:38","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:38"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:38","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:38"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"07:23:39"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"07:23:39"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:39","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:39"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"07:23:39"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:39","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:39"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":79,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:40","updated_at":"2025-06-24 11:23:29"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:40"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"07:23:40"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"07:23:40"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: w","timestamp":"07:23:40"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: A for action: w\u001b[22m\u001b[39m","timestamp":"07:23:40"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:23:40"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:23:40"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:40"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:23:40"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"wasteland\",\"message\":\"You are in a wasteland. Nothing for you to do here... you'd better go on!\",\"energy\":0},\"currentCoords\":\"53, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":80,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:40","updated_at":"2025-06-24 11:23:40"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:40"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:40"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"wasteland\",\"message\":\"You are in a wasteland. Nothing for you to do here... you'd better go on!\",\"energy\":0},\"currentCoords\":\"53, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":80,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:41","updated_at":"2025-06-24 11:23:40"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:41"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:23:41"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:23:41"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"wasteland\",\"message\":\"You are in a wasteland. Nothing for you to do here... you'd better go on!\",\"energy\":0},\"currentCoords\":\"53, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":80,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:41","updated_at":"2025-06-24 11:23:40"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:41"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:23:42"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:23:42"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:42"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:23:42"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":81,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:42","updated_at":"2025-06-24 11:23:42"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:42"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":81,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:42","updated_at":"2025-06-24 11:23:42"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:42"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":81,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:43","updated_at":"2025-06-24 11:23:42"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:43"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"07:23:43"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"07:23:43"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:23:43"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:23:43"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"07:23:43"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Cave\",\"message\":\"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.\",\"energy\":0},\"currentCoords\":\"54, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":81,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:43","updated_at":"2025-06-24 11:23:42"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:43"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:23:44"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:23:44"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:44"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:23:44"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"This is the place for you to gather more energy. The more energy, the more units in your army!!!\",\"energy\":0},\"currentCoords\":\"55, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":82,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:44","updated_at":"2025-06-24 11:23:44"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:44"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"This is the place for you to gather more energy. The more energy, the more units in your army!!!\",\"energy\":0},\"currentCoords\":\"55, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":82,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:45","updated_at":"2025-06-24 11:23:44"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:45"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:23:45"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:23:45"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:23:45"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:23:45"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:23:45"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"You know what they say... Not even 1,397 Joules of Energy can buy happiness... but it sure does make you feel good!\",\"energy\":0},\"currentCoords\":\"55, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":83,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:45","updated_at":"2025-06-24 11:23:45"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:45"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":34601,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"You know what they say... Not even 1,397 Joules of Energy can buy happiness... but it sure does make you feel good!\",\"energy\":0},\"currentCoords\":\"55, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":83,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:46","updated_at":"2025-06-24 11:23:45"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:46"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:23:46"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:23:46"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:23:46"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:23:46"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:46"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:23:46"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":35998,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"56, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":84,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:46","updated_at":"2025-06-24 11:23:46"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:46"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:23:46"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:23:46"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:46"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":35998,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"56, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":84,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:47","updated_at":"2025-06-24 11:23:46"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:47"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":35998,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"56, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":84,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:47","updated_at":"2025-06-24 11:23:46"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:47"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":35998,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"56, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":84,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:48","updated_at":"2025-06-24 11:23:46"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:48"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:23:48"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:23:48"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:48"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":35998,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"56, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":84,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:48","updated_at":"2025-06-24 11:23:46"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:48"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":35998,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"56, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":84,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:49","updated_at":"2025-06-24 11:23:46"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:49"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":35998,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"56, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":84,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:49","updated_at":"2025-06-24 11:23:46"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:49"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:23:49"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:23:49"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:49"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":35998,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"56, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":84,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:50","updated_at":"2025-06-24 11:23:46"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:50"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":35998,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"56, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":84,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:50","updated_at":"2025-06-24 11:23:46"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:50"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":35998,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"56, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":84,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:51","updated_at":"2025-06-24 11:23:46"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:51"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:23:51"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:23:51"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:51"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":35998,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"56, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":84,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:51","updated_at":"2025-06-24 11:23:46"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:51"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":35998,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"56, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":84,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:52","updated_at":"2025-06-24 11:23:46"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:52"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:23:52"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:23:52"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:23:52"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:23:52"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:52"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:23:52"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":35998,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"This is the place for you to gather more energy. The more energy, the more units in your army!!!\",\"energy\":0},\"currentCoords\":\"57, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":85,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:52","updated_at":"2025-06-24 11:23:52"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:52"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:23:52"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:23:52"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:52"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":35998,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"This is the place for you to gather more energy. The more energy, the more units in your army!!!\",\"energy\":0},\"currentCoords\":\"57, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":85,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:53","updated_at":"2025-06-24 11:23:52"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:53"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: w","timestamp":"07:23:53"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: A for action: w\u001b[22m\u001b[39m","timestamp":"07:23:53"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":35998,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"This is the place for you to gather more energy. The more energy, the more units in your army!!!\",\"energy\":0},\"currentCoords\":\"57, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":85,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:53","updated_at":"2025-06-24 11:23:52"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:53"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:23:53"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:23:53"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:53"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:23:53"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:23:54"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:23:54"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"56, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":86,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:54","updated_at":"2025-06-24 11:23:53"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:54"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:54"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"56, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":86,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:54","updated_at":"2025-06-24 11:23:53"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:54"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"56, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":86,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:55","updated_at":"2025-06-24 11:23:53"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:55"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:23:55"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:23:55"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":24858,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"56, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":86,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:55","updated_at":"2025-06-24 11:23:53"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:55"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:23:55"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:23:55"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:55"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:23:56"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:23:56"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:56"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:23:56"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":26318,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"This is the place for you to gather more energy. The more energy, the more units in your army!!!\",\"energy\":0},\"currentCoords\":\"57, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":87,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:56","updated_at":"2025-06-24 11:23:56"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:56"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":26318,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"This is the place for you to gather more energy. The more energy, the more units in your army!!!\",\"energy\":0},\"currentCoords\":\"57, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":87,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:56","updated_at":"2025-06-24 11:23:56"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:56"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:23:57"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:23:57"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":26318,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"This is the place for you to gather more energy. The more energy, the more units in your army!!!\",\"energy\":0},\"currentCoords\":\"57, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":87,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:57","updated_at":"2025-06-24 11:23:56"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:57"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:23:57"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:23:57"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:57"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:57"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:23:57"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":26318,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"BummerIt seems like you hit an empty ground!You didn't get anything, try again some other time, alright?\",\"energy\":0},\"currentCoords\":\"57, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":88,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:57","updated_at":"2025-06-24 11:23:57"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:57"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:23:58"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:23:58"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":26318,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"BummerIt seems like you hit an empty ground!You didn't get anything, try again some other time, alright?\",\"energy\":0},\"currentCoords\":\"57, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":88,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:58","updated_at":"2025-06-24 11:23:57"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:58"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:23:58"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:23:58"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:58"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:23:58"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:23:58"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:23:58"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":26318,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"58, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":89,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:58","updated_at":"2025-06-24 11:23:58"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:58"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:23:58"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":26318,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"58, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":89,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:59","updated_at":"2025-06-24 11:23:58"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:59"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:23:59"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:23:59"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:23:59"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":26318,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"58, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":89,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:23:59","updated_at":"2025-06-24 11:23:58"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:23:59"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:23:59"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:59"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:23:59"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:24:00"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:24:00"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"This is the place for you to gather more energy. The more energy, the more units in your army!!!\",\"energy\":0},\"currentCoords\":\"59, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:23 am\"}","id":90,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:00","updated_at":"2025-06-24 11:23:59"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:00"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:24:00"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:24:00"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:24:00"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:24:00"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:24:00"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Eureka!!!!!All that hard work has finally paid off. Your digging crew found 884 Joules of Energy, now keep on digging for more elsewhere!\",\"energy\":0},\"currentCoords\":\"59, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":91,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:00","updated_at":"2025-06-24 11:24:00"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:00"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:24:01"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:24:01"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Eureka!!!!!All that hard work has finally paid off. Your digging crew found 884 Joules of Energy, now keep on digging for more elsewhere!\",\"energy\":0},\"currentCoords\":\"59, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":91,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:01","updated_at":"2025-06-24 11:24:00"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:01"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:24:01"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:24:01"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Eureka!!!!!All that hard work has finally paid off. Your digging crew found 884 Joules of Energy, now keep on digging for more elsewhere!\",\"energy\":0},\"currentCoords\":\"59, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":91,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:01","updated_at":"2025-06-24 11:24:00"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:01"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:24:01"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:24:02"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:24:02"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Eureka!!!!!All that hard work has finally paid off. Your digging crew found 884 Joules of Energy, now keep on digging for more elsewhere!\",\"energy\":0},\"currentCoords\":\"59, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":91,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:02","updated_at":"2025-06-24 11:24:00"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:02"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Eureka!!!!!All that hard work has finally paid off. Your digging crew found 884 Joules of Energy, now keep on digging for more elsewhere!\",\"energy\":0},\"currentCoords\":\"59, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":91,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:02","updated_at":"2025-06-24 11:24:00"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:02"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:24:03"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:24:03"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:24:03"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:24:03"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Eureka!!!!!All that hard work has finally paid off. Your digging crew found 884 Joules of Energy, now keep on digging for more elsewhere!\",\"energy\":0},\"currentCoords\":\"59, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":91,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:03","updated_at":"2025-06-24 11:24:00"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:03"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:24:03"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Eureka!!!!!All that hard work has finally paid off. Your digging crew found 884 Joules of Energy, now keep on digging for more elsewhere!\",\"energy\":0},\"currentCoords\":\"59, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":91,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:03","updated_at":"2025-06-24 11:24:00"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:03"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Eureka!!!!!All that hard work has finally paid off. Your digging crew found 884 Joules of Energy, now keep on digging for more elsewhere!\",\"energy\":0},\"currentCoords\":\"59, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":91,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:04","updated_at":"2025-06-24 11:24:00"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:04"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:24:04"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:24:04"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Eureka!!!!!All that hard work has finally paid off. Your digging crew found 884 Joules of Energy, now keep on digging for more elsewhere!\",\"energy\":0},\"currentCoords\":\"59, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":91,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:04","updated_at":"2025-06-24 11:24:00"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:04"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:24:04"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Eureka!!!!!All that hard work has finally paid off. Your digging crew found 884 Joules of Energy, now keep on digging for more elsewhere!\",\"energy\":0},\"currentCoords\":\"59, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":91,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:05","updated_at":"2025-06-24 11:24:00"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:05"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Eureka!!!!!All that hard work has finally paid off. Your digging crew found 884 Joules of Energy, now keep on digging for more elsewhere!\",\"energy\":0},\"currentCoords\":\"59, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":91,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:05","updated_at":"2025-06-24 11:24:00"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:05"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:24:06"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:24:06"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:24:06"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:24:06"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":36928,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Geothermal Vents\",\"message\":\"Eureka!!!!!All that hard work has finally paid off. Your digging crew found 884 Joules of Energy, now keep on digging for more elsewhere!\",\"energy\":0},\"currentCoords\":\"59, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":91,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:06","updated_at":"2025-06-24 11:24:00"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:06"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:24:06"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:24:06"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:24:06"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:24:06"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:24:06"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:06","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:06"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:07","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:07"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:24:07"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:24:07"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:07","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:07"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:24:08"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:08","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:08"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:08","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:08"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:24:09"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:24:09"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:09","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:09"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:24:09"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:09","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:09"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:10","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:10"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:24:10"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:24:10"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:11","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:11"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:24:11"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:11","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:11"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:12","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:12"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:24:12"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:24:12"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:24:12"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:12","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:12"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:13","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:13"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:13","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:13"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:24:13"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:24:13"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:24:14"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:14","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:14"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:14","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:14"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:15","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:15"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:24:15"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:24:15"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:24:15"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:15","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:15"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:16","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:16"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:16","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:16"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:24:16"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:24:16"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:24:17"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:17","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:17"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:17","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:17"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:18","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:18"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:18","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:18"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:19","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:19"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:19","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:19"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:20","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:20"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:20","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:20"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:21","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:21"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:21","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:21"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:22","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:22"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:22","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:22"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:23","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:23"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:23","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:23"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:24","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:24"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:24","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:24"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:25","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:25"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:25","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:25"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:26","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:26"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:26","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:26"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:27","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:27"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:27","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:27"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:28","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:28"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:28","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:28"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:29","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:29"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:29","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:29"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:30","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:30"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:30","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:30"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:31","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:31"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:31","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:31"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:32","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:32"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:32","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:32"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:33","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:33"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:33","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:33"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:34","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:34"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:34","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:34"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:35","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:35"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:36","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:36"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:36","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:36"}
{"data":"{\"stats\":{\"rank\":34,\"power\":399730,\"strength\":90884,\"defense\":86662,\"base\":\"81, 73\",\"factories\":4},\"battleLog\":{\"attack\":137,\"defense\":12,\"pike\":74,\"land_mines\":0},\"resourcePanel\":{\"metal\":27843,\"metal_banked\":0,\"energy\":37812,\"energy_banked\":0},\"inventory\":{\"current\":11,\"capacity\":1950},\"shrineBuffs\":{\"spades\":false,\"hearts\":false,\"diamonds\":false,\"clubs\":false,\"totalBonus\":1},\"tile\":{\"title\":\"Metal Deposit\",\"message\":\"Need a new pike? Go get some Metal for it! This is the place for gathering that.\",\"energy\":0},\"currentCoords\":\"63, 120\",\"troopTransport\":false,\"date\":\"6-24-2025\",\"time\":\"07:24 am\"}","id":92,"level":"debug","message":"[DB] Raw data from DB:","timestamp":"07:24:37","updated_at":"2025-06-24 11:24:06"}
{"level":"debug","message":"[SIDEBAR] Parsed data for sidebar:","timestamp":"07:24:37"}
{"level":"info","message":"Already logged in.","timestamp":"07:28:07"}
{"level":"info","message":"Injecting sidebar overlay...","timestamp":"07:28:07"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Sidebar overlay script injected.\u001b[22m\u001b[39m","timestamp":"07:28:07"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:07"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:07"}
{"battleLog":{"attack":137,"defense":12,"land_mines":0,"pike":74},"currentCoords":"63, 120","date":"6-24-2025","inventory":{"capacity":1950,"current":11},"level":"info","message":"[SCRAPE] Scraped game data:","resourcePanel":{"energy":37812,"energy_banked":0,"metal":27843,"metal_banked":0},"shrineBuffs":{"clubs":false,"diamonds":false,"hearts":false,"spades":false,"totalBonus":1},"stats":{"base":"81, 73","defense":86662,"factories":4,"power":399730,"rank":34,"strength":90884},"tile":{"energy":0,"message":"Need a new pike? Go get some Metal for it! This is the place for gathering that.","title":"Metal Deposit"},"time":"07:28 am","timestamp":"07:28:07","troopTransport":false}
{"level":"info","message":"Bot is running. Browser will remain open.","timestamp":"07:28:07"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:07"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:07"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:28:11"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:28:11"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:11"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:28:12"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:28:12"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:13"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:13"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:13"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:13"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:13"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:13"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:28:14"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:28:14"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:14"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:14"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:14"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:14"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:15"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:15"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:15"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:15"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:15"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:28:15"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:28:15"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:16"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:16"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:16"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:16"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:16"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:16"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:28:17"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:17"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:28:17"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:17"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:17"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:17"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:18"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:18"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:28:18"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:28:18"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:19"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:19"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:19"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:19"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"07:28:20"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"07:28:20"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"07:28:20"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"07:28:21"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"07:28:21"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"07:28:22"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"07:28:23"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"07:28:23"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"07:28:23"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:24"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:24"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:24"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"07:28:24"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"07:28:24"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:24"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:24"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:25"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: w","timestamp":"07:28:26"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: A for action: w\u001b[22m\u001b[39m","timestamp":"07:28:26"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:26"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:26"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:26"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"07:28:27"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"07:28:27"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"07:28:28"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"07:28:29"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"07:28:29"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:29"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"07:28:29"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:29"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:30"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:30"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:30"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:30"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:30"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:31"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:31"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:31"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:31"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:31"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:28:32"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:28:32"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:32"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:28:33"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:28:33"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:34"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:28:35"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:28:35"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:35"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:35"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:35"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:36"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:36"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:36"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: w","timestamp":"07:28:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: A for action: w\u001b[22m\u001b[39m","timestamp":"07:28:37"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:37"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:28:38"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:28:38"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:38"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:28:39"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:28:39"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:40"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:41"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:41"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:28:41"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:28:41"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:41"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:41"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:41"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:41"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:41"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:41"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:41"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:41"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:41"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"07:28:42"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"07:28:42"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:42"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:42"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"07:28:43"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:43"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:43"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:43"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:43"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:43"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:28:44"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:28:44"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:44"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:44"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:44"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:45"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:45"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:45"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:46"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:46"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:46"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:46"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:46"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"07:28:47"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"07:28:47"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:47"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:47"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:47"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:47"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:48"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:48"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:48"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:48"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:48"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:49"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:49"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"07:28:49"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"07:28:49"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"07:28:49"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:28:50"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:28:50"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:50"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:51"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:51"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:28:51"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:28:51"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:52"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:28:53"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:28:53"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:53"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"07:28:54"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"07:28:54"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:28:54"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:28:54"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"07:28:55"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"07:28:56"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"07:28:56"}
{"level":"error","message":"Auto-farm error for metal: page.waitForTimeout: Target page, context or browser has been closed","stack":"page.waitForTimeout: Target page, context or browser has been closed\n    at D:\\BRAIN\\automation\\scrape.ts:390:18","timestamp":"07:28:56"}
{"level":"info","message":"Already logged in.","timestamp":"13:11:42"}
{"level":"info","message":"Injecting sidebar overlay...","timestamp":"13:11:42"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Sidebar overlay script injected.\u001b[22m\u001b[39m","timestamp":"13:11:42"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:11:42"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:11:42"}
{"battleLog":{"attack":139,"defense":12,"land_mines":0,"pike":75},"currentCoords":"110, 1","date":"6-24-2025","inventory":{"capacity":1950,"current":18},"level":"info","message":"[SCRAPE] Scraped game data:","resourcePanel":{"energy":62578,"energy_banked":0,"metal":125906,"metal_banked":0},"shrineBuffs":{"clubs":false,"diamonds":false,"hearts":false,"spades":false,"totalBonus":1},"stats":{"base":"81, 73","defense":87062,"factories":4,"power":399366,"rank":34,"strength":90123},"tile":{"energy":0,"message":"You are in a wasteland. Nothing for you to do here... you'd better go on!","title":"wasteland"},"time":"01:11 pm","timestamp":"13:11:42","troopTransport":false}
{"level":"info","message":"Bot is running. Browser will remain open.","timestamp":"13:11:42"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:11:42"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:11:42"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:11:49"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:11:49"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:11:49"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:11:49"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:11:49"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"13:11:52"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"13:11:52"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:11:52"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"13:11:53"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"13:11:53"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:11:53"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: w","timestamp":"13:11:54"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: A for action: w\u001b[22m\u001b[39m","timestamp":"13:11:54"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:11:54"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:11:54"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:11:54"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:11:55"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:11:55"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:11:55"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:11:55"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:11:55"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"13:11:56"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"13:11:56"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:11:56"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:11:57"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:11:57"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:11:57"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:11:57"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:11:57"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"13:11:58"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"13:11:58"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:11:58"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:11:58"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:11:58"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:11:58"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:11:59"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:11:59"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"13:11:59"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"13:11:59"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:11:59"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:00"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:00"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"13:12:01"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"13:12:01"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:01"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:01"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:12:01"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:01"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:01"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:01"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:02"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"13:12:02"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:02"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"13:12:02"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:02"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:02"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:02"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:12:02"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:04"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:04"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"13:12:04"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"13:12:04"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:12:04"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:05"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:05"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"13:12:05"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"13:12:05"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:12:05"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:05"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:05"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:05"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:06"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:06"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"13:12:07"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"13:12:07"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:07"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:07"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:07"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:12:07"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:08"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:08"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"13:12:08"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"13:12:08"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:08"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:08"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:08"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:12:08"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:09"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:09"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:09"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:09"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:09"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:11"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:11"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:14"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:14"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:14"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:14"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:14"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"13:12:14"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"13:12:14"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"13:12:14"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"13:12:16"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"13:12:16"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"13:12:16"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:16"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:16"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:16"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:16"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:16"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:17"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:17"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:17"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:17"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:17"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:18"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:18"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:18"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:18"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:18"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"13:12:19"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"13:12:19"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"13:12:19"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:19"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:19"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:19"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:19"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:19"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:20"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:20"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:20"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:20"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:20"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:21"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:21"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:21"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:21"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:21"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:22"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:22"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:22"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:22"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:22"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:22"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:22"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:23"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:23"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:23"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"13:12:23"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"13:12:23"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:12:23"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:24"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:24"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"13:12:25"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"13:12:25"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:12:25"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:25"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:25"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:26"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:26"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"13:12:26"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"13:12:26"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:26"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:26"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:26"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:12:26"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:27"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:27"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:27"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:27"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:27"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"13:12:28"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"13:12:28"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:28"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:28"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"13:12:28"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:28"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:28"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:28"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:29"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:29"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"13:12:29"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"13:12:29"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"13:12:29"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:30"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:30"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:30"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:30"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:30"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"13:12:31"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"13:12:31"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:12:31"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:32"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:32"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"13:12:32"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"13:12:32"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:32"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:32"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:32"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:12:32"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:12:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:12:33"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"13:12:34"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"13:12:34"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:12:34"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:12:34"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:12:34"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:12:34"}
{"level":"error","message":"Error updating sidebar: page.evaluate: Target page, context or browser has been closed","stack":"page.evaluate: Target page, context or browser has been closed\n    at Timeout.updateSidebar (D:\\BRAIN\\automation\\scrape.ts:454:18)","timestamp":"13:12:35"}
{"level":"info","message":"Already logged in.","timestamp":"13:46:40"}
{"level":"info","message":"Injecting sidebar overlay...","timestamp":"13:46:40"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Sidebar overlay script injected.\u001b[22m\u001b[39m","timestamp":"13:46:40"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:46:40"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:46:41"}
{"battleLog":{"attack":139,"defense":12,"land_mines":0,"pike":75},"currentCoords":"140, 1","date":"6-24-2025","inventory":{"capacity":1950,"current":18},"level":"info","message":"[SCRAPE] Scraped game data:","resourcePanel":{"energy":65389,"energy_banked":0,"metal":125906,"metal_banked":0},"shrineBuffs":{"clubs":false,"diamonds":false,"hearts":false,"spades":false,"totalBonus":1},"stats":{"base":"81, 73","defense":87062,"factories":4,"power":399366,"rank":34,"strength":90123},"tile":{"energy":0,"message":"Need a new pike? Go get some Metal for it! This is the place for gathering that.","title":"Metal Deposit"},"time":"01:46 pm","timestamp":"13:46:41","troopTransport":false}
{"level":"info","message":"Bot is running. Browser will remain open.","timestamp":"13:46:41"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:46:41"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:46:41"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:46:42"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:46:42"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:46:43"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:46:43"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:46:44"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:46:44"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:46:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:46:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:46:45"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:46:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:46:45"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:46:46"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:46:46"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:46:49"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:46:49"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:46:50"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:46:50"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:46:51"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:46:51"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:46:51"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:46:51"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:46:51"}
{"level":"info","message":"Already logged in.","timestamp":"13:49:15"}
{"level":"info","message":"Injecting sidebar overlay...","timestamp":"13:49:15"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Sidebar overlay script injected.\u001b[22m\u001b[39m","timestamp":"13:49:15"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:49:15"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:49:15"}
{"battleLog":{"attack":139,"defense":12,"land_mines":0,"pike":75},"currentCoords":"148, 1","date":"6-24-2025","inventory":{"capacity":1950,"current":18},"level":"info","message":"[SCRAPE] Scraped game data:","resourcePanel":{"energy":65389,"energy_banked":0,"metal":125906,"metal_banked":0},"shrineBuffs":{"clubs":false,"diamonds":false,"hearts":false,"spades":false,"totalBonus":1},"stats":{"base":"81, 73","defense":87062,"factories":4,"power":399366,"rank":34,"strength":90123},"tile":{"energy":0,"message":"You are in a wasteland. Nothing for you to do here... you'd better go on!","title":"wasteland"},"time":"01:49 pm","timestamp":"13:49:15","troopTransport":false}
{"level":"info","message":"Bot is running. Browser will remain open.","timestamp":"13:49:15"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:49:15"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:49:15"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:49:19"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:49:19"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:49:19"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:49:19"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:49:19"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"13:49:22"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"13:49:22"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:49:22"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:49:22"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:49:22"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:49:22"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"13:49:23"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"13:49:23"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:49:24"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:49:24"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:49:24"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"13:49:25"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"13:49:25"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:49:25"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:49:25"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:49:25"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:49:25"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"13:49:26"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"13:49:26"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"13:49:27"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:49:27"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:49:27"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"13:49:28"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"13:49:28"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"13:49:28"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:49:28"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:49:28"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:49:28"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:49:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:49:29"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:49:30"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:49:30"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:49:30"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"13:49:31"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"13:49:31"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"13:49:31"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:49:31"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:49:31"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:49:32"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:49:32"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:49:32"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"13:49:32"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"13:49:32"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:49:33"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:49:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:49:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:49:33"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:49:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:49:33"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:49:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:49:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:49:33"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"13:49:34"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"13:49:34"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:49:34"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:49:34"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:49:34"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:49:35"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:49:35"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:49:35"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:49:36"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:49:36"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:49:36"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:49:36"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:49:36"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"13:49:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"13:49:37"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"13:49:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"13:49:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"13:49:37"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"13:49:38"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"13:49:38"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:49:39"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"13:49:40"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"13:49:40"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:49:40"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"13:49:41"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"13:49:41"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:49:42"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"13:49:43"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"13:49:43"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"13:49:43"}
{"level":"info","message":"Already logged in.","timestamp":"14:27:56"}
{"level":"info","message":"Injecting sidebar overlay...","timestamp":"14:27:56"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Sidebar overlay script injected.\u001b[22m\u001b[39m","timestamp":"14:27:56"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:27:56"}
{"battleLog":{"attack":139,"defense":12,"land_mines":0,"pike":75},"currentCoords":"7, 1","date":"6-24-2025","inventory":{"capacity":1950,"current":18},"level":"info","message":"[SCRAPE] Scraped game data:","resourcePanel":{"energy":67717,"energy_banked":0,"metal":125906,"metal_banked":0},"shrineBuffs":{"clubs":false,"diamonds":false,"hearts":false,"spades":false,"totalBonus":1},"stats":{"base":"81, 73","defense":87062,"factories":4,"power":399366,"rank":34,"strength":90123},"tile":{"energy":0,"message":"Need a new pike? Go get some Metal for it! This is the place for gathering that.","title":"Metal Deposit"},"time":"02:27 pm","timestamp":"14:27:56","troopTransport":false}
{"level":"info","message":"Bot is running. Browser will remain open.","timestamp":"14:27:56"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:27:56"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:27:56"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:27:56"}
{"level":"info","message":"Already logged in.","timestamp":"14:34:57"}
{"level":"info","message":"Injecting sidebar overlay...","timestamp":"14:34:57"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Sidebar overlay script injected.\u001b[22m\u001b[39m","timestamp":"14:34:57"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:34:57"}
{"battleLog":{"attack":139,"defense":12,"land_mines":0,"pike":75},"currentCoords":"7, 1","date":"6-24-2025","inventory":{"capacity":1950,"current":18},"level":"info","message":"[SCRAPE] Scraped game data:","resourcePanel":{"energy":67717,"energy_banked":0,"metal":125906,"metal_banked":0},"shrineBuffs":{"clubs":false,"diamonds":false,"hearts":false,"spades":false,"totalBonus":1},"stats":{"base":"81, 73","defense":87062,"factories":4,"power":399366,"rank":34,"strength":90123},"tile":{"energy":0,"message":"Need a new pike? Go get some Metal for it! This is the place for gathering that.","title":"Metal Deposit"},"time":"02:34 pm","timestamp":"14:34:57","troopTransport":false}
{"level":"info","message":"Bot is running. Browser will remain open.","timestamp":"14:34:57"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:34:57"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:34:57"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:34:57"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:35:04"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:35:04"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:35:04"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:35:04"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:35:04"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:35:05"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:35:05"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:35:05"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:35:05"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:35:05"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:35:07"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:35:07"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:35:07"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:35:07"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:35:07"}
{"level":"info","message":"Already logged in.","timestamp":"14:37:37"}
{"level":"info","message":"Injecting sidebar overlay...","timestamp":"14:37:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Sidebar overlay script injected.\u001b[22m\u001b[39m","timestamp":"14:37:37"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:37:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:37:37"}
{"battleLog":{"attack":139,"defense":12,"land_mines":0,"pike":75},"currentCoords":"10, 1","date":"6-24-2025","inventory":{"capacity":1950,"current":18},"level":"info","message":"[SCRAPE] Scraped game data:","resourcePanel":{"energy":67717,"energy_banked":0,"metal":125906,"metal_banked":0},"shrineBuffs":{"clubs":false,"diamonds":false,"hearts":false,"spades":false,"totalBonus":1},"stats":{"base":"81, 73","defense":87062,"factories":4,"power":399366,"rank":34,"strength":90123},"tile":{"energy":0,"message":"You are in a wasteland. Nothing for you to do here... you'd better go on!","title":"wasteland"},"time":"02:37 pm","timestamp":"14:37:37","troopTransport":false}
{"level":"info","message":"Bot is running. Browser will remain open.","timestamp":"14:37:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:37:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:37:37"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:37:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:37:45"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:37:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:37:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:37:45"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:37:48"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:37:48"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:37:48"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:37:49"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:37:49"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:37:50"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:37:51"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:37:51"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:37:51"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:37:52"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:37:52"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:37:53"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:37:54"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:37:54"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:37:54"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:37:55"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:37:55"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:37:55"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:37:55"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:37:55"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:37:56"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:37:56"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:37:57"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:37:57"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:37:57"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"14:37:57"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"14:37:57"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"14:37:57"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:37:57"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:37:57"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:37:58"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:37:58"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:37:58"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:37:58"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:37:58"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:37:59"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:37:59"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:37:59"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:38:00"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:38:00"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:38:00"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:38:01"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:38:01"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:38:02"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:38:02"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:38:02"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:02"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:02"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:02"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:38:03"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:38:03"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:03"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:03"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:03"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:38:04"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:38:04"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:38:04"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:38:04"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:04"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:04"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:04"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:38:05"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:38:05"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:38:05"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:05"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:05"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:05"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:38:06"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:38:06"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:06"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:38:06"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:06"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:06"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:38:07"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:38:07"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:07"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:38:07"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:38:07"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:07"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:07"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:08"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:38:08"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:08"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:08"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:38:08"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:38:08"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:09"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:09"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:09"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:38:09"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:38:09"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:38:09"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:38:10"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:38:10"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:38:10"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:38:10"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:38:10"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:38:10"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:38:11"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:38:11"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:38:11"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:11"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:11"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:11"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:38:12"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:38:12"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:12"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:12"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:12"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:38:13"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:38:13"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:38:13"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:38:13"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:13"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:14"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:14"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"14:38:14"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"14:38:15"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"14:38:15"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: w","timestamp":"14:38:15"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: A for action: w\u001b[22m\u001b[39m","timestamp":"14:38:15"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"14:38:15"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:15"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:15"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:15"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:38:16"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:38:16"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:38:17"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:17"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:17"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:17"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:38:18"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:38:18"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:38:18"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:38:18"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:18"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:18"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:18"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"14:38:18"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:38:19"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:38:19"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:19"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:19"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:19"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:38:20"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:38:20"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:20"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:20"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:20"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:38:21"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:38:21"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:38:21"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:38:21"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:38:21"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:38:21"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:38:22"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:38:22"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:38:23"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:38:24"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:38:24"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:38:24"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:38:25"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:38:25"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:38:26"}
{"level":"info","message":"Already logged in.","timestamp":"14:39:29"}
{"level":"info","message":"Injecting sidebar overlay...","timestamp":"14:39:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Sidebar overlay script injected.\u001b[22m\u001b[39m","timestamp":"14:39:29"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:39:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:39:29"}
{"battleLog":{"attack":139,"defense":12,"land_mines":0,"pike":75},"currentCoords":"28, 1","date":"6-24-2025","inventory":{"capacity":1950,"current":18},"level":"info","message":"[SCRAPE] Scraped game data:","resourcePanel":{"energy":73230,"energy_banked":0,"metal":125906,"metal_banked":0},"shrineBuffs":{"clubs":false,"diamonds":false,"hearts":false,"spades":false,"totalBonus":1},"stats":{"base":"81, 73","defense":87062,"factories":4,"power":399366,"rank":34,"strength":90123},"tile":{"energy":0,"message":"This is the place for you to gather more energy. The more energy, the more units in your army!!!","title":"Geothermal Vents"},"time":"02:39 pm","timestamp":"14:39:29","troopTransport":false}
{"level":"info","message":"Bot is running. Browser will remain open.","timestamp":"14:39:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:39:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:39:29"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:39:30"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:39:30"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:39:31"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:39:31"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:39:31"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:39:31"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:39:32"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:39:32"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:39:32"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:39:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:39:33"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:39:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:39:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:39:33"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:39:33"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:39:33"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:39:34"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:39:34"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:39:34"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:39:34"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:39:34"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:39:34"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:39:35"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:39:35"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:39:35"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:39:35"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:39:35"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:39:36"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:39:36"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:39:37"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:39:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:39:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:39:37"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:39:38"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:39:38"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:39:38"}
{"level":"info","message":"Already logged in.","timestamp":"14:41:26"}
{"level":"info","message":"Injecting sidebar overlay...","timestamp":"14:41:26"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Sidebar overlay script injected.\u001b[22m\u001b[39m","timestamp":"14:41:26"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:41:26"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:41:26"}
{"battleLog":{"attack":139,"defense":12,"land_mines":0,"pike":75},"currentCoords":"40, 1","date":"6-24-2025","inventory":{"capacity":1950,"current":18},"level":"info","message":"[SCRAPE] Scraped game data:","resourcePanel":{"energy":74614,"energy_banked":0,"metal":125906,"metal_banked":0},"shrineBuffs":{"clubs":false,"diamonds":false,"hearts":false,"spades":false,"totalBonus":1},"stats":{"base":"81, 73","defense":87062,"factories":4,"power":399366,"rank":34,"strength":90123},"tile":{"energy":0,"message":"In front of you is a cave. The entrance is blocked by a heavy rock. It will cost you 100 energy to enter the cave and see whats inside it.","title":"Cave"},"time":"02:41 pm","timestamp":"14:41:26","troopTransport":false}
{"level":"info","message":"Bot is running. Browser will remain open.","timestamp":"14:41:26"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:41:26"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:41:26"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"14:41:29"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"14:41:29"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"14:41:29"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"14:41:31"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"14:41:31"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"14:41:31"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"14:41:32"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"14:41:32"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:41:32"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:41:32"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"14:41:32"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:41:32"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:41:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:41:33"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:41:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:41:33"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:41:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:41:34"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:41:34"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:41:34"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:41:34"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:41:34"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:41:34"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:41:34"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:41:34"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:41:35"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:41:35"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:41:35"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:41:35"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:41:35"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"14:41:35"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"14:41:35"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"14:41:35"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"14:41:37"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm cave","timestamp":"14:41:37"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"14:41:37"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:41:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:41:37"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:41:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:41:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:41:37"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:41:38"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:41:38"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:41:39"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:41:39"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:41:40"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:41:40"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:41:41"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:41:41"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:41:41"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:41:41"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:41:41"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:41:41"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:41:43"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:41:43"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:41:43"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:41:43"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:41:43"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:41:43"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:41:43"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:41:43"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:41:44"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:41:44"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:41:44"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:41:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:41:45"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:41:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:41:46"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:41:46"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:41:46"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:41:46"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:41:46"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:41:47"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:41:47"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:41:47"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:41:48"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:41:48"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:41:48"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:41:48"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:41:48"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:41:49"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:41:49"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:41:49"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:41:49"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:41:49"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:41:50"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:41:50"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:41:50"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:41:50"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:41:50"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:41:50"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:41:52"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:41:52"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:41:52"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:41:52"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:41:52"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:41:53"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:41:53"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:41:53"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:41:53"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:41:53"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:41:53"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:41:55"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:41:55"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:41:55"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:41:56"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:41:56"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:41:56"}
{"level":"info","message":"Already logged in.","timestamp":"14:43:50"}
{"level":"info","message":"Injecting sidebar overlay...","timestamp":"14:43:50"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Sidebar overlay script injected.\u001b[22m\u001b[39m","timestamp":"14:43:50"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:43:50"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:43:50"}
{"battleLog":{"attack":139,"defense":12,"land_mines":0,"pike":75},"currentCoords":"52, 1","date":"6-24-2025","inventory":{"capacity":1950,"current":18},"level":"info","message":"[SCRAPE] Scraped game data:","resourcePanel":{"energy":78775,"energy_banked":0,"metal":125906,"metal_banked":0},"shrineBuffs":{"clubs":false,"diamonds":false,"hearts":false,"spades":false,"totalBonus":1},"stats":{"base":"81, 73","defense":87062,"factories":4,"power":399366,"rank":34,"strength":90123},"tile":{"energy":0,"message":"Need a new pike? Go get some Metal for it! This is the place for gathering that.","title":"Metal Deposit"},"time":"02:43 pm","timestamp":"14:43:50","troopTransport":false}
{"level":"info","message":"Bot is running. Browser will remain open.","timestamp":"14:43:50"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:43:50"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:43:50"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:43:53"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:43:53"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:43:53"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:43:54"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:43:54"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:43:55"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:43:55"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:43:55"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:43:55"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:43:55"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:43:55"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:43:56"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:43:56"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:43:56"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:43:56"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:43:57"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:43:57"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:43:57"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:43:57"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:43:57"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:43:58"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:43:58"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:43:58"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:43:58"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:43:58"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:43:58"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:43:59"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:43:59"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:43:59"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:43:59"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:43:59"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:43:59"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:43:59"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:43:59"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:43:59"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:44:00"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:44:00"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:44:00"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:44:00"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:44:00"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:44:00"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:44:00"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:44:01"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:44:02"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:44:02"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:44:02"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:44:03"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:44:03"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:44:03"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm metal","timestamp":"14:44:03"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:44:04"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:44:04"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:44:04"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:44:05"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:44:05"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:44:05"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:44:05"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:44:05"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:44:05"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:44:05"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:44:05"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:44:05"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:44:06"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:44:06"}
{"level":"error","message":"Auto-farm error for energy: page.waitForTimeout: Target page, context or browser has been closed","stack":"page.waitForTimeout: Target page, context or browser has been closed\n    at D:\\BRAIN\\automation\\scrape.ts:399:18","timestamp":"14:44:07"}
{"level":"info","message":"Already logged in.","timestamp":"14:44:58"}
{"level":"info","message":"Injecting sidebar overlay...","timestamp":"14:44:58"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Sidebar overlay script injected.\u001b[22m\u001b[39m","timestamp":"14:44:58"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:44:58"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:44:58"}
{"battleLog":{"attack":139,"defense":12,"land_mines":0,"pike":75},"currentCoords":"59, 1","date":"6-24-2025","inventory":{"capacity":1950,"current":18},"level":"info","message":"[SCRAPE] Scraped game data:","resourcePanel":{"energy":82714,"energy_banked":0,"metal":125906,"metal_banked":0},"shrineBuffs":{"clubs":false,"diamonds":false,"hearts":false,"spades":false,"totalBonus":1},"stats":{"base":"81, 73","defense":87062,"factories":4,"power":399366,"rank":34,"strength":90123},"tile":{"energy":0,"message":"This is the place for you to gather more energy. The more energy, the more units in your army!!!","title":"Geothermal Vents"},"time":"02:44 pm","timestamp":"14:44:58","troopTransport":false}
{"level":"info","message":"Bot is running. Browser will remain open.","timestamp":"14:44:58"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:44:58"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:44:58"}
{"level":"info","message":"Already logged in.","timestamp":"14:45:22"}
{"level":"info","message":"Injecting sidebar overlay...","timestamp":"14:45:22"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Sidebar overlay script injected.\u001b[22m\u001b[39m","timestamp":"14:45:22"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:22"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:23"}
{"battleLog":{"attack":139,"defense":12,"land_mines":0,"pike":75},"currentCoords":"59, 1","date":"6-24-2025","inventory":{"capacity":1950,"current":18},"level":"info","message":"[SCRAPE] Scraped game data:","resourcePanel":{"energy":82714,"energy_banked":0,"metal":125906,"metal_banked":0},"shrineBuffs":{"clubs":false,"diamonds":false,"hearts":false,"spades":false,"totalBonus":1},"stats":{"base":"81, 73","defense":87062,"factories":4,"power":399366,"rank":34,"strength":90123},"tile":{"energy":0,"message":"This is the place for you to gather more energy. The more energy, the more units in your army!!!","title":"Geothermal Vents"},"time":"02:45 pm","timestamp":"14:45:23","troopTransport":false}
{"level":"info","message":"Bot is running. Browser will remain open.","timestamp":"14:45:23"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:23"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:23"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:45:25"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:45:25"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:25"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:45:25"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:25"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:25"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:45:26"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:45:26"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:45:26"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:45:26"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:45:26"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:45:27"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:45:27"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:28"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:45:28"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:45:28"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:28"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:28"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:45:28"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:45:28"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:45:28"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:29"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:45:29"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:45:29"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:45:29"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:45:30"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:45:30"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:45:31"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:45:31"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:31"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:45:31"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:31"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:31"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:45:31"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:45:31"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:32"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:32"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:32"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:45:32"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:45:32"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:33"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:45:34"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm metal","timestamp":"14:45:34"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:45:34"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:45:35"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm metal","timestamp":"14:45:35"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:45:35"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:45:35"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:35"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:45:35"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:35"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:35"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:45:36"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:45:36"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:37"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:45:38"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:45:38"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:45:38"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:38"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:38"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:38"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:45:39"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:45:39"}
{"level":"info","message":"Already logged in.","timestamp":"14:45:39"}
{"level":"info","message":"Injecting sidebar overlay...","timestamp":"14:45:39"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Sidebar overlay script injected.\u001b[22m\u001b[39m","timestamp":"14:45:39"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:39"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:39"}
{"battleLog":{"attack":139,"defense":12,"land_mines":0,"pike":75},"currentCoords":"67, 1","date":"6-24-2025","inventory":{"capacity":1950,"current":18},"level":"info","message":"[SCRAPE] Scraped game data:","resourcePanel":{"energy":86095,"energy_banked":0,"metal":127002,"metal_banked":0},"shrineBuffs":{"clubs":false,"diamonds":false,"hearts":false,"spades":false,"totalBonus":1},"stats":{"base":"81, 73","defense":87062,"factories":4,"power":399366,"rank":34,"strength":90123},"tile":{"energy":0,"message":"This is the place for you to gather more energy. The more energy, the more units in your army!!!","title":"Geothermal Vents"},"time":"02:45 pm","timestamp":"14:45:39","troopTransport":false}
{"level":"info","message":"Bot is running. Browser will remain open.","timestamp":"14:45:39"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:39"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:39"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:39"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:39"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:39"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:45:40"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm metal","timestamp":"14:45:40"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:45:40"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:45:41"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:45:41"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:41"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:41"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:41"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:45:41"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:45:41"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:45:41"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:41"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:42"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:42"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:45:42"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:45:42"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:42"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:42"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:42"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:45:43"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:45:43"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:45:43"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:43"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:43"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:43"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:45:43"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:45:43"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:43"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:44"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:44"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"14:45:44"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm cave","timestamp":"14:45:44"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"14:45:44"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:45:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:45:45"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:45"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:46"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:46"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:45:48"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:45:48"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:48"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:48"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:48"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:45:49"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:45:49"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:45:49"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:45:49"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:45:49"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:45:49"}
{"level":"info","message":"Already logged in.","timestamp":"14:46:00"}
{"level":"info","message":"Injecting sidebar overlay...","timestamp":"14:46:00"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Sidebar overlay script injected.\u001b[22m\u001b[39m","timestamp":"14:46:00"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:01"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:01"}
{"battleLog":{"attack":139,"defense":12,"land_mines":0,"pike":75},"currentCoords":"73, 1","date":"6-24-2025","inventory":{"capacity":1950,"current":18},"level":"info","message":"[SCRAPE] Scraped game data:","resourcePanel":{"energy":90162,"energy_banked":0,"metal":127999,"metal_banked":0},"shrineBuffs":{"clubs":false,"diamonds":false,"hearts":false,"spades":false,"totalBonus":1},"stats":{"base":"81, 73","defense":87074,"factories":4,"power":399409,"rank":34,"strength":90123},"tile":{"energy":0,"message":"This is the place for you to gather more energy. The more energy, the more units in your army!!!","title":"Geothermal Vents"},"time":"02:46 pm","timestamp":"14:46:01","troopTransport":false}
{"level":"info","message":"Bot is running. Browser will remain open.","timestamp":"14:46:01"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:01"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:01"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:46:06"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:46:06"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:06"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:46:06"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:06"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:06"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:07"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:07"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:46:07"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:46:07"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:46:08"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:08"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:08"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:46:09"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:46:09"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:09"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:09"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:09"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:09"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:09"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:46:09"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:10"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:10"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:11"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:11"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:11"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:11"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:11"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:46:12"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm metal","timestamp":"14:46:12"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:46:12"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:13"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:13"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:13"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:13"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:13"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"14:46:13"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm cave","timestamp":"14:46:13"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:46:14"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:14"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:14"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:14"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:14"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:14"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:15"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:15"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:15"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:15"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:15"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:17"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:17"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:18"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:18"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:18"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:18"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:18"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:19"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:19"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:19"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:19"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:19"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:20"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:20"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:20"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:20"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:20"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"14:46:21"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm cave","timestamp":"14:46:21"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:46:21"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:22"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:22"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:22"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:22"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:22"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:23"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:23"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:23"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:23"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:23"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:46:24"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm metal","timestamp":"14:46:24"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:46:24"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:24"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:24"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:25"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:25"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:25"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:46:25"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:46:25"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:26"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:46:26"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:26"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:26"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:26"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:26"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: energy","timestamp":"14:46:27"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm energy","timestamp":"14:46:27"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:46:27"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:27"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:27"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:27"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:27"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:27"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:46:28"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm metal","timestamp":"14:46:28"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:46:29"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:29"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:29"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:29"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"14:46:30"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm cave","timestamp":"14:46:30"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:46:30"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:30"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:30"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:31"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:31"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:31"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:33"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:33"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:33"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:46:34"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm metal","timestamp":"14:46:34"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:46:35"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:35"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:35"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:35"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:35"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:35"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:37"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:37"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:37"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: cave","timestamp":"14:46:37"}
{"level":"info","message":"[AUTO-FARM] Pressed F to farm cave","timestamp":"14:46:37"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=100, hasGatherButton=false","timestamp":"14:46:38"}
{"level":"info","message":"[MOVEMENT] Controller action triggered: e","timestamp":"14:46:38"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Pressed key: D for action: e\u001b[22m\u001b[39m","timestamp":"14:46:38"}
{"level":"info","message":"[SCRAPE] Re-scraping game data after movement...","timestamp":"14:46:39"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Game data saved to D:\\BRAIN\\data\\data.json and inserted into SQLite DB.\u001b[22m\u001b[39m","timestamp":"14:46:39"}
{"level":"info","message":"\u001b[32m\u001b[1m✓ Movement-triggered scrape completed.\u001b[22m\u001b[39m","timestamp":"14:46:39"}
{"level":"info","message":"[AUTO-FARM] Auto-farm triggered for: metal","timestamp":"14:46:39"}
{"level":"info","message":"[AUTO-FARM] Pressed G to farm metal","timestamp":"14:46:39"}
{"level":"info","message":"[AUTO-FARM] Auto-farm result: success=true, amount=0, hasGatherButton=false","timestamp":"14:46:39"}
