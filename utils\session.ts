// ...existing code...
// Utility for session/cookie storage
import { BrowserContext } from 'playwright';
import * as path from 'path';
import { log } from './logger';

export async function saveStorageState(context: BrowserContext, filename = 'cookies.json') {
  const COOKIES_PATH = path.resolve(__dirname, '../automation', filename);
  await context.storageState({ path: COOKIES_PATH });
  log.info('Session cookies saved to', { path: COOKIES_PATH });
}
// ...existing code...
