# @fortawesome/fontawesome-free - The Official Font Awesome 6 NPM package

> "I came here to chew bubblegum and install Font Awesome 6 - and I'm all out of bubblegum"

[![npm](https://img.shields.io/npm/v/@fortawesome/fontawesome-free.svg?style=flat-square)](https://www.npmjs.com/package/@fortawesome/fontawesome-free)

## Installation

```
$ npm i --save @fortawesome/fontawesome-free
```

Or

```
$ yarn add @fortawesome/fontawesome-free
```

## What's included?

**This package includes all the same files available through our Free and Pro CDN.**

* /js - All JavaScript files associated with Font Awesome 6 SVG with JS
* /css - All CSS using the classic Web Fonts with CSS implementation
* /sprites - SVG icons packaged in a convenient sprite
* /scss, /less - CSS Pre-processor files for Web Fonts with CSS
* /webfonts - Accompanying files for Web Fonts with CSS
* /svg - Individual icon files in SVG format

## Documentation

Get started [here](https://docs.fontawesome.com/web/setup/get-started). Continue your journey [here](https://docs.fontawesome.com/web/setup/packages).

Or go straight to the [API documentation](https://docs.fontawesome.com/apis/javascript/get-started).

## Issues and support

Start with [GitHub issues](https://github.com/FortAwesome/Font-Awesome/issues) and ping us on [Twitter](https://twitter.com/fontawesome) if you need to.
