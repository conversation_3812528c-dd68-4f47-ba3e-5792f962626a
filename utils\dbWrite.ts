/**
 * OVERVIEW: SQLite DB initialization and insert utility for ECHO bot
 * Ensures the DB and table exist, and inserts new scrape data.
 */
import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import * as fs from 'fs';

const DB_PATH = './data/data.sqlite3';
const TABLE_NAME = 'scraped_data';

export async function initDb() {
  const db = await open({ filename: DB_PATH, driver: sqlite3.Database });
  await db.exec(`CREATE TABLE IF NOT EXISTS ${TABLE_NAME} (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    data TEXT NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`);
  await db.close();
}

export async function insertScrapeData(data: any) {
  const db = await open({ filename: DB_PATH, driver: sqlite3.Database });
  await db.run(
    `INSERT INTO ${TABLE_NAME} (data) VALUES (?)`,
    JSON.stringify(data)
  );
  await db.close();
}
