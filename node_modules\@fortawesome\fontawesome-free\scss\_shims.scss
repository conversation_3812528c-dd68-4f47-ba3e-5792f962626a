.#{$fa-css-prefix}.#{$fa-css-prefix}-glass { #{$fa-icon-property}: unquote("\"#{ $fa-var-martini-glass-empty }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-envelope-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-envelope-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-envelope }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-star-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-star-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-star }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-remove { #{$fa-icon-property}: unquote("\"#{ $fa-var-xmark }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-close { #{$fa-icon-property}: unquote("\"#{ $fa-var-xmark }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-gear { #{$fa-icon-property}: unquote("\"#{ $fa-var-gear }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-trash-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-trash-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-trash-can }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-home { #{$fa-icon-property}: unquote("\"#{ $fa-var-house }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-file }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-clock-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-clock-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-clock }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-arrow-circle-o-down {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-arrow-circle-o-down { #{$fa-icon-property}: unquote("\"#{ $fa-var-circle-down }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-arrow-circle-o-up {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-arrow-circle-o-up { #{$fa-icon-property}: unquote("\"#{ $fa-var-circle-up }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-play-circle-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-play-circle-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-circle-play }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-repeat { #{$fa-icon-property}: unquote("\"#{ $fa-var-arrow-rotate-right }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-rotate-right { #{$fa-icon-property}: unquote("\"#{ $fa-var-arrow-rotate-right }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-refresh { #{$fa-icon-property}: unquote("\"#{ $fa-var-arrows-rotate }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-list-alt {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-list-alt { #{$fa-icon-property}: unquote("\"#{ $fa-var-rectangle-list }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-dedent { #{$fa-icon-property}: unquote("\"#{ $fa-var-outdent }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-video-camera { #{$fa-icon-property}: unquote("\"#{ $fa-var-video }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-picture-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-picture-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-image }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-photo {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-photo { #{$fa-icon-property}: unquote("\"#{ $fa-var-image }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-image {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-image { #{$fa-icon-property}: unquote("\"#{ $fa-var-image }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-map-marker { #{$fa-icon-property}: unquote("\"#{ $fa-var-location-dot }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-pencil-square-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-pencil-square-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-pen-to-square }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-edit {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-edit { #{$fa-icon-property}: unquote("\"#{ $fa-var-pen-to-square }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-share-square-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-share-from-square }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-check-square-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-check-square-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-check }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-arrows { #{$fa-icon-property}: unquote("\"#{ $fa-var-up-down-left-right }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-times-circle-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-times-circle-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-circle-xmark }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-check-circle-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-check-circle-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-circle-check }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-mail-forward { #{$fa-icon-property}: unquote("\"#{ $fa-var-share }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-expand { #{$fa-icon-property}: unquote("\"#{ $fa-var-up-right-and-down-left-from-center }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-compress { #{$fa-icon-property}: unquote("\"#{ $fa-var-down-left-and-up-right-to-center }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-eye {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-eye-slash {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-warning { #{$fa-icon-property}: unquote("\"#{ $fa-var-triangle-exclamation }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-calendar { #{$fa-icon-property}: unquote("\"#{ $fa-var-calendar-days }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-arrows-v { #{$fa-icon-property}: unquote("\"#{ $fa-var-up-down }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-arrows-h { #{$fa-icon-property}: unquote("\"#{ $fa-var-left-right }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-bar-chart { #{$fa-icon-property}: unquote("\"#{ $fa-var-chart-column }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-bar-chart-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-chart-column }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-twitter-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-twitter-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-twitter }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-facebook-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-facebook-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-facebook }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-gears { #{$fa-icon-property}: unquote("\"#{ $fa-var-gears }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-thumbs-o-up {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-thumbs-o-up { #{$fa-icon-property}: unquote("\"#{ $fa-var-thumbs-up }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-thumbs-o-down {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-thumbs-o-down { #{$fa-icon-property}: unquote("\"#{ $fa-var-thumbs-down }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-heart-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-heart-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-heart }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-sign-out { #{$fa-icon-property}: unquote("\"#{ $fa-var-right-from-bracket }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-linkedin-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-linkedin-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-linkedin }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-thumb-tack { #{$fa-icon-property}: unquote("\"#{ $fa-var-thumbtack }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-external-link { #{$fa-icon-property}: unquote("\"#{ $fa-var-up-right-from-square }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-sign-in { #{$fa-icon-property}: unquote("\"#{ $fa-var-right-to-bracket }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-github-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-github-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-github }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-lemon-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-lemon-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-lemon }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-square-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-square-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-square }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-bookmark-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-bookmark-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-bookmark }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-twitter {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-facebook {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-facebook { #{$fa-icon-property}: unquote("\"#{ $fa-var-facebook-f }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-facebook-f {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-facebook-f { #{$fa-icon-property}: unquote("\"#{ $fa-var-facebook-f }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-github {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-credit-card {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-feed { #{$fa-icon-property}: unquote("\"#{ $fa-var-rss }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hdd-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hdd-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-hard-drive }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-o-right {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-o-right { #{$fa-icon-property}: unquote("\"#{ $fa-var-hand-point-right }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-o-left {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-o-left { #{$fa-icon-property}: unquote("\"#{ $fa-var-hand-point-left }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-o-up {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-o-up { #{$fa-icon-property}: unquote("\"#{ $fa-var-hand-point-up }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-o-down {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-o-down { #{$fa-icon-property}: unquote("\"#{ $fa-var-hand-point-down }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-globe { #{$fa-icon-property}: unquote("\"#{ $fa-var-earth-americas }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-tasks { #{$fa-icon-property}: unquote("\"#{ $fa-var-bars-progress }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-arrows-alt { #{$fa-icon-property}: unquote("\"#{ $fa-var-maximize }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-group { #{$fa-icon-property}: unquote("\"#{ $fa-var-users }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-chain { #{$fa-icon-property}: unquote("\"#{ $fa-var-link }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-cut { #{$fa-icon-property}: unquote("\"#{ $fa-var-scissors }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-files-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-files-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-copy }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-floppy-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-floppy-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-floppy-disk }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-save {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-save { #{$fa-icon-property}: unquote("\"#{ $fa-var-floppy-disk }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-navicon { #{$fa-icon-property}: unquote("\"#{ $fa-var-bars }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-reorder { #{$fa-icon-property}: unquote("\"#{ $fa-var-bars }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-magic { #{$fa-icon-property}: unquote("\"#{ $fa-var-wand-magic-sparkles }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-pinterest {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-pinterest-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-pinterest-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-pinterest }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-google-plus-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-google-plus-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-google-plus }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-google-plus {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-google-plus { #{$fa-icon-property}: unquote("\"#{ $fa-var-google-plus-g }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-money { #{$fa-icon-property}: unquote("\"#{ $fa-var-money-bill-1 }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-unsorted { #{$fa-icon-property}: unquote("\"#{ $fa-var-sort }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-sort-desc { #{$fa-icon-property}: unquote("\"#{ $fa-var-sort-down }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-sort-asc { #{$fa-icon-property}: unquote("\"#{ $fa-var-sort-up }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-linkedin {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-linkedin { #{$fa-icon-property}: unquote("\"#{ $fa-var-linkedin-in }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-rotate-left { #{$fa-icon-property}: unquote("\"#{ $fa-var-arrow-rotate-left }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-legal { #{$fa-icon-property}: unquote("\"#{ $fa-var-gavel }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-tachometer { #{$fa-icon-property}: unquote("\"#{ $fa-var-gauge-high }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-dashboard { #{$fa-icon-property}: unquote("\"#{ $fa-var-gauge-high }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-comment-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-comment-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-comment }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-comments-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-comments-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-comments }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-flash { #{$fa-icon-property}: unquote("\"#{ $fa-var-bolt }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-clipboard { #{$fa-icon-property}: unquote("\"#{ $fa-var-paste }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-lightbulb-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-lightbulb-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-lightbulb }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-exchange { #{$fa-icon-property}: unquote("\"#{ $fa-var-right-left }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-cloud-download { #{$fa-icon-property}: unquote("\"#{ $fa-var-cloud-arrow-down }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-cloud-upload { #{$fa-icon-property}: unquote("\"#{ $fa-var-cloud-arrow-up }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-bell-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-bell-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-bell }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-cutlery { #{$fa-icon-property}: unquote("\"#{ $fa-var-utensils }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-text-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-text-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-file-lines }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-building-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-building-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-building }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hospital-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hospital-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-hospital }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-tablet { #{$fa-icon-property}: unquote("\"#{ $fa-var-tablet-screen-button }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-mobile { #{$fa-icon-property}: unquote("\"#{ $fa-var-mobile-screen-button }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-mobile-phone { #{$fa-icon-property}: unquote("\"#{ $fa-var-mobile-screen-button }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-circle-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-circle-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-circle }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-mail-reply { #{$fa-icon-property}: unquote("\"#{ $fa-var-reply }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-github-alt {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-folder-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-folder-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-folder }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-folder-open-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-folder-open-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-folder-open }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-smile-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-smile-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-face-smile }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-frown-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-frown-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-face-frown }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-meh-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-meh-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-face-meh }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-keyboard-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-keyboard-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-keyboard }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-flag-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-flag-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-flag }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-mail-reply-all { #{$fa-icon-property}: unquote("\"#{ $fa-var-reply-all }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-star-half-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-star-half-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-star-half-stroke }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-star-half-empty {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-star-half-empty { #{$fa-icon-property}: unquote("\"#{ $fa-var-star-half-stroke }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-star-half-full {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-star-half-full { #{$fa-icon-property}: unquote("\"#{ $fa-var-star-half-stroke }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-code-fork { #{$fa-icon-property}: unquote("\"#{ $fa-var-code-branch }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-chain-broken { #{$fa-icon-property}: unquote("\"#{ $fa-var-link-slash }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-unlink { #{$fa-icon-property}: unquote("\"#{ $fa-var-link-slash }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-calendar-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-calendar-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-calendar }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-maxcdn {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-html5 {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-css3 {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-unlock-alt { #{$fa-icon-property}: unquote("\"#{ $fa-var-unlock }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-minus-square-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-minus-square-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-minus }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-level-up { #{$fa-icon-property}: unquote("\"#{ $fa-var-turn-up }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-level-down { #{$fa-icon-property}: unquote("\"#{ $fa-var-turn-down }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-pencil-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-pen }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-external-link-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-up-right }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-compass {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-caret-square-o-down {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-caret-square-o-down { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-caret-down }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-toggle-down {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-toggle-down { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-caret-down }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-caret-square-o-up {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-caret-square-o-up { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-caret-up }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-toggle-up {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-toggle-up { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-caret-up }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-caret-square-o-right {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-caret-square-o-right { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-caret-right }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-toggle-right {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-toggle-right { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-caret-right }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-eur { #{$fa-icon-property}: unquote("\"#{ $fa-var-euro-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-euro { #{$fa-icon-property}: unquote("\"#{ $fa-var-euro-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-gbp { #{$fa-icon-property}: unquote("\"#{ $fa-var-sterling-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-usd { #{$fa-icon-property}: unquote("\"#{ $fa-var-dollar-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-dollar { #{$fa-icon-property}: unquote("\"#{ $fa-var-dollar-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-inr { #{$fa-icon-property}: unquote("\"#{ $fa-var-indian-rupee-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-rupee { #{$fa-icon-property}: unquote("\"#{ $fa-var-indian-rupee-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-jpy { #{$fa-icon-property}: unquote("\"#{ $fa-var-yen-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-cny { #{$fa-icon-property}: unquote("\"#{ $fa-var-yen-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-rmb { #{$fa-icon-property}: unquote("\"#{ $fa-var-yen-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-yen { #{$fa-icon-property}: unquote("\"#{ $fa-var-yen-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-rub { #{$fa-icon-property}: unquote("\"#{ $fa-var-ruble-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-ruble { #{$fa-icon-property}: unquote("\"#{ $fa-var-ruble-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-rouble { #{$fa-icon-property}: unquote("\"#{ $fa-var-ruble-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-krw { #{$fa-icon-property}: unquote("\"#{ $fa-var-won-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-won { #{$fa-icon-property}: unquote("\"#{ $fa-var-won-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-btc {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-bitcoin {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-bitcoin { #{$fa-icon-property}: unquote("\"#{ $fa-var-btc }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-text { #{$fa-icon-property}: unquote("\"#{ $fa-var-file-lines }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-sort-alpha-asc { #{$fa-icon-property}: unquote("\"#{ $fa-var-arrow-down-a-z }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-sort-alpha-desc { #{$fa-icon-property}: unquote("\"#{ $fa-var-arrow-down-z-a }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-sort-amount-asc { #{$fa-icon-property}: unquote("\"#{ $fa-var-arrow-down-short-wide }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-sort-amount-desc { #{$fa-icon-property}: unquote("\"#{ $fa-var-arrow-down-wide-short }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-sort-numeric-asc { #{$fa-icon-property}: unquote("\"#{ $fa-var-arrow-down-1-9 }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-sort-numeric-desc { #{$fa-icon-property}: unquote("\"#{ $fa-var-arrow-down-9-1 }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-youtube-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-youtube-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-youtube }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-youtube {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-xing {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-xing-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-xing-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-xing }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-youtube-play {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-youtube-play { #{$fa-icon-property}: unquote("\"#{ $fa-var-youtube }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-dropbox {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-stack-overflow {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-instagram {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-flickr {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-adn {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-bitbucket {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-bitbucket-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-bitbucket-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-bitbucket }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-tumblr {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-tumblr-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-tumblr-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-tumblr }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-long-arrow-down { #{$fa-icon-property}: unquote("\"#{ $fa-var-down-long }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-long-arrow-up { #{$fa-icon-property}: unquote("\"#{ $fa-var-up-long }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-long-arrow-left { #{$fa-icon-property}: unquote("\"#{ $fa-var-left-long }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-long-arrow-right { #{$fa-icon-property}: unquote("\"#{ $fa-var-right-long }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-apple {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-windows {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-android {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-linux {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-dribbble {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-skype {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-foursquare {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-trello {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-gratipay {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-gittip {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-gittip { #{$fa-icon-property}: unquote("\"#{ $fa-var-gratipay }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-sun-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-sun-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-sun }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-moon-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-moon-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-moon }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-vk {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-weibo {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-renren {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-pagelines {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-stack-exchange {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-arrow-circle-o-right {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-arrow-circle-o-right { #{$fa-icon-property}: unquote("\"#{ $fa-var-circle-right }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-arrow-circle-o-left {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-arrow-circle-o-left { #{$fa-icon-property}: unquote("\"#{ $fa-var-circle-left }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-caret-square-o-left {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-caret-square-o-left { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-caret-left }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-toggle-left {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-toggle-left { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-caret-left }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-dot-circle-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-dot-circle-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-circle-dot }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-vimeo-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-vimeo-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-vimeo }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-try { #{$fa-icon-property}: unquote("\"#{ $fa-var-turkish-lira-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-turkish-lira { #{$fa-icon-property}: unquote("\"#{ $fa-var-turkish-lira-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-plus-square-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-plus-square-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-plus }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-slack {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-wordpress {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-openid {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-institution { #{$fa-icon-property}: unquote("\"#{ $fa-var-building-columns }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-bank { #{$fa-icon-property}: unquote("\"#{ $fa-var-building-columns }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-mortar-board { #{$fa-icon-property}: unquote("\"#{ $fa-var-graduation-cap }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-yahoo {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-google {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-reddit {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-reddit-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-reddit-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-reddit }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-stumbleupon-circle {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-stumbleupon {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-delicious {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-digg {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-pied-piper-pp {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-pied-piper-alt {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-drupal {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-joomla {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-behance {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-behance-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-behance-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-behance }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-steam {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-steam-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-steam-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-steam }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-automobile { #{$fa-icon-property}: unquote("\"#{ $fa-var-car }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-cab { #{$fa-icon-property}: unquote("\"#{ $fa-var-taxi }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-spotify {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-deviantart {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-soundcloud {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-pdf-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-pdf-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-file-pdf }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-word-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-word-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-file-word }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-excel-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-excel-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-file-excel }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-powerpoint-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-powerpoint-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-file-powerpoint }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-image-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-image-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-file-image }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-photo-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-photo-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-file-image }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-picture-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-picture-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-file-image }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-archive-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-archive-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-file-zipper }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-zip-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-zip-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-file-zipper }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-audio-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-audio-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-file-audio }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-sound-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-sound-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-file-audio }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-video-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-video-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-file-video }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-movie-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-movie-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-file-video }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-code-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-file-code-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-file-code }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-vine {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-codepen {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-jsfiddle {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-life-bouy { #{$fa-icon-property}: unquote("\"#{ $fa-var-life-ring }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-life-buoy { #{$fa-icon-property}: unquote("\"#{ $fa-var-life-ring }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-life-saver { #{$fa-icon-property}: unquote("\"#{ $fa-var-life-ring }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-support { #{$fa-icon-property}: unquote("\"#{ $fa-var-life-ring }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-circle-o-notch { #{$fa-icon-property}: unquote("\"#{ $fa-var-circle-notch }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-rebel {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-ra {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-ra { #{$fa-icon-property}: unquote("\"#{ $fa-var-rebel }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-resistance {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-resistance { #{$fa-icon-property}: unquote("\"#{ $fa-var-rebel }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-empire {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-ge {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-ge { #{$fa-icon-property}: unquote("\"#{ $fa-var-empire }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-git-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-git-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-git }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-git {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hacker-news {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-y-combinator-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-y-combinator-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-hacker-news }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-yc-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-yc-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-hacker-news }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-tencent-weibo {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-qq {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-weixin {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-wechat {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-wechat { #{$fa-icon-property}: unquote("\"#{ $fa-var-weixin }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-send { #{$fa-icon-property}: unquote("\"#{ $fa-var-paper-plane }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-paper-plane-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-paper-plane-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-paper-plane }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-send-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-send-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-paper-plane }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-circle-thin {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-circle-thin { #{$fa-icon-property}: unquote("\"#{ $fa-var-circle }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-header { #{$fa-icon-property}: unquote("\"#{ $fa-var-heading }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-futbol-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-futbol-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-futbol }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-soccer-ball-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-soccer-ball-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-futbol }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-slideshare {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-twitch {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-yelp {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-newspaper-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-newspaper-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-newspaper }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-paypal {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-google-wallet {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-cc-visa {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-cc-mastercard {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-cc-discover {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-cc-amex {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-cc-paypal {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-cc-stripe {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-bell-slash-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-bell-slash-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-bell-slash }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-trash { #{$fa-icon-property}: unquote("\"#{ $fa-var-trash-can }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-copyright {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-eyedropper { #{$fa-icon-property}: unquote("\"#{ $fa-var-eye-dropper }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-area-chart { #{$fa-icon-property}: unquote("\"#{ $fa-var-chart-area }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-pie-chart { #{$fa-icon-property}: unquote("\"#{ $fa-var-chart-pie }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-line-chart { #{$fa-icon-property}: unquote("\"#{ $fa-var-chart-line }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-lastfm {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-lastfm-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-lastfm-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-lastfm }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-ioxhost {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-angellist {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-cc {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-cc { #{$fa-icon-property}: unquote("\"#{ $fa-var-closed-captioning }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-ils { #{$fa-icon-property}: unquote("\"#{ $fa-var-shekel-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-shekel { #{$fa-icon-property}: unquote("\"#{ $fa-var-shekel-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-sheqel { #{$fa-icon-property}: unquote("\"#{ $fa-var-shekel-sign }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-buysellads {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-connectdevelop {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-dashcube {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-forumbee {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-leanpub {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-sellsy {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-shirtsinbulk {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-simplybuilt {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-skyatlas {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-diamond {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-diamond { #{$fa-icon-property}: unquote("\"#{ $fa-var-gem }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-transgender { #{$fa-icon-property}: unquote("\"#{ $fa-var-mars-and-venus }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-intersex { #{$fa-icon-property}: unquote("\"#{ $fa-var-mars-and-venus }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-transgender-alt { #{$fa-icon-property}: unquote("\"#{ $fa-var-transgender }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-facebook-official {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-facebook-official { #{$fa-icon-property}: unquote("\"#{ $fa-var-facebook }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-pinterest-p {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-whatsapp {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hotel { #{$fa-icon-property}: unquote("\"#{ $fa-var-bed }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-viacoin {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-medium {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-y-combinator {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-yc {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-yc { #{$fa-icon-property}: unquote("\"#{ $fa-var-y-combinator }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-optin-monster {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-opencart {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-expeditedssl {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-battery-4 { #{$fa-icon-property}: unquote("\"#{ $fa-var-battery-full }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-battery { #{$fa-icon-property}: unquote("\"#{ $fa-var-battery-full }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-battery-3 { #{$fa-icon-property}: unquote("\"#{ $fa-var-battery-three-quarters }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-battery-2 { #{$fa-icon-property}: unquote("\"#{ $fa-var-battery-half }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-battery-1 { #{$fa-icon-property}: unquote("\"#{ $fa-var-battery-quarter }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-battery-0 { #{$fa-icon-property}: unquote("\"#{ $fa-var-battery-empty }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-object-group {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-object-ungroup {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-sticky-note-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-sticky-note-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-note-sticky }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-cc-jcb {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-cc-diners-club {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-clone {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hourglass-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-hourglass }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hourglass-1 { #{$fa-icon-property}: unquote("\"#{ $fa-var-hourglass-start }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hourglass-2 { #{$fa-icon-property}: unquote("\"#{ $fa-var-hourglass-half }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hourglass-3 { #{$fa-icon-property}: unquote("\"#{ $fa-var-hourglass-end }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-rock-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-rock-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-hand-back-fist }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-grab-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-grab-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-hand-back-fist }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-paper-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-paper-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-hand }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-stop-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-stop-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-hand }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-scissors-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-scissors-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-hand-scissors }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-lizard-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-lizard-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-hand-lizard }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-spock-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-spock-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-hand-spock }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-pointer-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-pointer-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-hand-pointer }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-peace-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-hand-peace-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-hand-peace }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-registered {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-creative-commons {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-gg {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-gg-circle {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-odnoklassniki {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-odnoklassniki-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-odnoklassniki-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-odnoklassniki }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-get-pocket {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-wikipedia-w {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-safari {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-chrome {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-firefox {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-opera {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-internet-explorer {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-television { #{$fa-icon-property}: unquote("\"#{ $fa-var-tv }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-contao {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-500px {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-amazon {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-calendar-plus-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-calendar-plus-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-calendar-plus }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-calendar-minus-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-calendar-minus-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-calendar-minus }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-calendar-times-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-calendar-times-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-calendar-xmark }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-calendar-check-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-calendar-check-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-calendar-check }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-map-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-map-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-map }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-commenting { #{$fa-icon-property}: unquote("\"#{ $fa-var-comment-dots }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-commenting-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-commenting-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-comment-dots }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-houzz {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-vimeo {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-vimeo { #{$fa-icon-property}: unquote("\"#{ $fa-var-vimeo-v }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-black-tie {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-fonticons {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-reddit-alien {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-edge {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-credit-card-alt { #{$fa-icon-property}: unquote("\"#{ $fa-var-credit-card }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-codiepie {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-modx {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-fort-awesome {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-usb {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-product-hunt {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-mixcloud {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-scribd {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-pause-circle-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-pause-circle-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-circle-pause }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-stop-circle-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-stop-circle-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-circle-stop }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-bluetooth {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-bluetooth-b {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-gitlab {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-wpbeginner {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-wpforms {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-envira {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-wheelchair-alt {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-wheelchair-alt { #{$fa-icon-property}: unquote("\"#{ $fa-var-accessible-icon }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-question-circle-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-question-circle-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-circle-question }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-volume-control-phone { #{$fa-icon-property}: unquote("\"#{ $fa-var-phone-volume }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-asl-interpreting { #{$fa-icon-property}: unquote("\"#{ $fa-var-hands-asl-interpreting }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-deafness { #{$fa-icon-property}: unquote("\"#{ $fa-var-ear-deaf }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-hard-of-hearing { #{$fa-icon-property}: unquote("\"#{ $fa-var-ear-deaf }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-glide {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-glide-g {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-signing { #{$fa-icon-property}: unquote("\"#{ $fa-var-hands }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-viadeo {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-viadeo-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-viadeo-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-viadeo }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-snapchat {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-snapchat-ghost {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-snapchat-ghost { #{$fa-icon-property}: unquote("\"#{ $fa-var-snapchat }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-snapchat-square {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-snapchat-square { #{$fa-icon-property}: unquote("\"#{ $fa-var-square-snapchat }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-pied-piper {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-first-order {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-yoast {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-themeisle {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-google-plus-official {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-google-plus-official { #{$fa-icon-property}: unquote("\"#{ $fa-var-google-plus }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-google-plus-circle {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-google-plus-circle { #{$fa-icon-property}: unquote("\"#{ $fa-var-google-plus }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-font-awesome {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-fa {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-fa { #{$fa-icon-property}: unquote("\"#{ $fa-var-font-awesome }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-handshake-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-handshake-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-handshake }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-envelope-open-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-envelope-open-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-envelope-open }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-linode {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-address-book-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-address-book-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-address-book }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-vcard { #{$fa-icon-property}: unquote("\"#{ $fa-var-address-card }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-address-card-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-address-card-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-address-card }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-vcard-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-vcard-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-address-card }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-user-circle-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-user-circle-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-circle-user }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-user-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-user-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-user }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-id-badge {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-drivers-license { #{$fa-icon-property}: unquote("\"#{ $fa-var-id-card }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-id-card-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-id-card-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-id-card }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-drivers-license-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-drivers-license-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-id-card }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-quora {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-free-code-camp {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-telegram {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-thermometer-4 { #{$fa-icon-property}: unquote("\"#{ $fa-var-temperature-full }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-thermometer { #{$fa-icon-property}: unquote("\"#{ $fa-var-temperature-full }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-thermometer-3 { #{$fa-icon-property}: unquote("\"#{ $fa-var-temperature-three-quarters }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-thermometer-2 { #{$fa-icon-property}: unquote("\"#{ $fa-var-temperature-half }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-thermometer-1 { #{$fa-icon-property}: unquote("\"#{ $fa-var-temperature-quarter }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-thermometer-0 { #{$fa-icon-property}: unquote("\"#{ $fa-var-temperature-empty }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-bathtub { #{$fa-icon-property}: unquote("\"#{ $fa-var-bath }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-s15 { #{$fa-icon-property}: unquote("\"#{ $fa-var-bath }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-window-maximize {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-window-restore {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-times-rectangle { #{$fa-icon-property}: unquote("\"#{ $fa-var-rectangle-xmark }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-window-close-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-window-close-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-rectangle-xmark }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-times-rectangle-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-times-rectangle-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-rectangle-xmark }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-bandcamp {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-grav {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-etsy {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-imdb {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-ravelry {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-eercast {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-eercast { #{$fa-icon-property}: unquote("\"#{ $fa-var-sellcast }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-snowflake-o {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-snowflake-o { #{$fa-icon-property}: unquote("\"#{ $fa-var-snowflake }\""); }
.#{$fa-css-prefix}.#{$fa-css-prefix}-superpowers {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-wpexplorer {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
.#{$fa-css-prefix}.#{$fa-css-prefix}-meetup {
  font-family: 'Font Awesome 6 Brands';
  font-weight: 400;
}
