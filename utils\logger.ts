/**
 * @file logger.ts
 * @overview Custom logger utility using <PERSON> with colors and formatting
 * Provides different log levels with color-coded output for better debugging
 */
import winston from 'winston';
import colors from 'colors';

// Enable colors
colors.enable();

// Define custom colors for log levels
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'cyan',
  debug: 'magenta',
  verbose: 'blue',
  silly: 'gray'
};

// Custom format function with colors
const coloredFormat = winston.format.printf(({ level, message, timestamp, ...meta }) => {
  // Apply colors based on log level
  let coloredLevel = level;
  let coloredMessage = String(message);
  
  switch (level) {
    case 'error':
      coloredLevel = colors.red.bold('[ERROR]');
      coloredMessage = colors.red(String(message));
      break;
    case 'warn':
      coloredLevel = colors.yellow.bold('[WARN]');
      coloredMessage = colors.yellow(String(message));
      break;
    case 'info':
      coloredLevel = colors.cyan.bold('[INFO]');
      coloredMessage = colors.cyan(String(message));
      break;
    case 'debug':
      coloredLevel = colors.magenta.bold('[DEBUG]');
      coloredMessage = colors.magenta(String(message));
      break;
    case 'verbose':
      coloredLevel = colors.blue.bold('[VERBOSE]');
      coloredMessage = colors.blue(String(message));
      break;
    case 'silly':
      coloredLevel = colors.gray.bold('[SILLY]');
      coloredMessage = colors.gray(String(message));
      break;
    default:
      coloredLevel = `[${level.toUpperCase()}]`;
  }

  const coloredTimestamp = colors.green(`${timestamp}`);
  
  // Format additional metadata if present
  const metaString = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
  
  return `${coloredTimestamp} ${coloredLevel} ${coloredMessage}${metaString}`;
});

// Create Winston logger instance
const logger = winston.createLogger({
  level: 'info', // Set default log level to reduce clutter
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    coloredFormat
  ),
  transports: [
    // Console transport with colors
    new winston.transports.Console({
      handleExceptions: true,
      handleRejections: true
    }),
    // File transport for errors
    new winston.transports.File({
      filename: './logs/error.log',
      level: 'error',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),
    // File transport for all logs
    new winston.transports.File({
      filename: './logs/combined.log',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  ],
  exitOnError: false
});

// Create logs directory if it doesn't exist
import * as fs from 'fs';
import * as path from 'path';

const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Export logger with convenient methods
export const log = {
  error: (message: string, meta?: any) => logger.error(message, meta),
  warn: (message: string, meta?: any) => logger.warn(message, meta),
  info: (message: string, meta?: any) => logger.info(message, meta),
  debug: (message: string, meta?: any) => logger.debug(message, meta),
  verbose: (message: string, meta?: any) => logger.verbose(message, meta),
  silly: (message: string, meta?: any) => logger.silly(message, meta),
  
  // Special methods for automation bot
  scrape: (message: string, data?: any) => logger.info(`[SCRAPE] ${message}`, data),
  autofarm: (message: string, data?: any) => logger.info(`[AUTO-FARM] ${message}`, data),
  sidebar: (message: string, data?: any) => logger.debug(`[SIDEBAR] ${message}`, data),
  movement: (message: string, data?: any) => logger.info(`[MOVEMENT] ${message}`, data),
  db: (message: string, data?: any) => logger.debug(`[DB] ${message}`, data),
  
  // Success and failure methods with colors
  success: (message: string, meta?: any) => {
    const successMessage = colors.green.bold(`✓ ${message}`);
    logger.info(successMessage, meta);
  },
  
  failure: (message: string, meta?: any) => {
    const failureMessage = colors.red.bold(`✗ ${message}`);
    logger.error(failureMessage, meta);
  },
  
  // Raw winston logger for advanced usage
  raw: logger
};

export default log;
