/**
 * @file format.ts
 * @overview Utility for flattening objects to a maximum of 2 levels deep for JSON output.
 * - Converts arrays of objects to objects with numeric keys
 * - Ensures no property is nested deeper than 2 levels
 * - Designed for use with scraper/game data output
 */

/**
 * Flattens an object to a maximum of 2 levels deep.
 * Arrays of objects become objects with numeric keys.
 * Deeper nested objects are stringified.
 * @param obj The input object
 * @returns The flattened object
 */
export function flattenToTwoLevels(obj: Record<string, any>): Record<string, any> {
  const result: Record<string, any> = {};
  for (const [key, value] of Object.entries(obj)) {
    if (Array.isArray(value)) {
      // Convert array of objects to object with numeric keys
      result[key] = {};
      value.forEach((item, idx) => {
        if (typeof item === 'object' && item !== null) {
          // Only flatten one more level
          result[key][idx] = {};
          for (const [k, v] of Object.entries(item)) {
            if (typeof v === 'object' && v !== null) {
              // Stringify deeper objects
              result[key][idx][k] = JSON.stringify(v);
            } else {
              result[key][idx][k] = v;
            }
          }
        } else {
          result[key][idx] = item;
        }
      });
    } else if (typeof value === 'object' && value !== null) {
      // Only flatten one more level
      result[key] = {};
      for (const [k, v] of Object.entries(value)) {
        if (typeof v === 'object' && v !== null) {
          // Stringify deeper objects
          result[key][k] = JSON.stringify(v);
        } else {
          result[key][k] = v;
        }
      }
    } else {
      result[key] = value;
    }
  }
  return result;
}
