/**
 * @file login.ts
 * @overview Playwright automation bot for sowclassic.com login
 * - Loads credentials from .env
 * - Uses Chromium only
 * - Persists session cookies to cookies.json
 * - Detects login state and avoids duplicate login
 * - Keeps browser open for further automation
 * - Uses ECHO-compliant /utils modules for all shared logic
 */
import { getEnvVar } from '../utils/env';
import { fileExists } from '../utils/file';
import { createMaximizedContext } from '../utils/playwright';
import { saveStorageState } from '../utils/session';
import { scrapeGameData, saveGameData, injectSidebar } from '../automation/scrape';
import { log } from '../utils/logger';
import { resolveDataPath } from '../utils/path';
import { flattenToTwoLevels } from '../utils/format';

const EMAIL = getEnvVar('EMAIL');
const PASSWORD = getEnvVar('PASSWORD');
const LOGIN_URL = 'https://sowclassic.com/game/play';
const COOKIES_PATH = resolveDataPath('../automation/cookies.json');

async function main() {
  // Load cookies if available
  const storageState = (await fileExists(COOKIES_PATH)) ? COOKIES_PATH : undefined;
  const { browser, context } = await createMaximizedContext(storageState);
  const page = await context.newPage();
  await page.goto(LOGIN_URL, { waitUntil: 'domcontentloaded' });

  // Detect login form
  const loginForm = await page.$('form#new_user');
  if (loginForm) {
    log.info('Login form detected. Attempting login...');
    await page.fill('input[name="user[email]"]', EMAIL);
    await page.fill('input[name="user[password]"]', PASSWORD);
    // Check the correct "Remember me" checkbox (skip hidden input)
    const rememberCheckbox = await page.$('input[type="checkbox"][name="user[remember_me]"]');
    if (rememberCheckbox) {
      await rememberCheckbox.check();
    } else {
      log.warn('Remember me checkbox not found!');
    }
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'domcontentloaded' }),
      page.click('input[type="submit"][value="Log in"]'),
    ]);
    // Check if login succeeded
    const stillOnLogin = await page.$('form#new_user');
    if (stillOnLogin) {
      log.failure('Login failed. Please check your credentials.');
    } else {
      log.success('Login successful!');
      await saveStorageState(context);
    }
  } else {
    log.info('Already logged in.');
  }
  // After login or if already logged in, scrape data and save to /data
  try {
    // Inject sidebar overlay before scraping
    await injectSidebar(page, './data/data.sqlite3');
    const data = await scrapeGameData(page);
    const flatData = flattenToTwoLevels(data);
    await saveGameData(flatData, 'data.json', page); // Pass page for direct sidebar update
    
    log.scrape('Scraped game data:', flatData);
  } catch (err) {
    log.error('Failed to scrape game data:', err);
  }

  // Keep browser open for further automation
  log.info('Bot is running. Browser will remain open.');
}

main().catch((err) => {
  log.error('Unexpected error:', err);
});
