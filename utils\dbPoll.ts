/**
 * OVERVIEW: SQLite3 polling utility for ECHO sidebar overlay
 * Polls the local SQLite3 DB for latest scraped data and provides it to <PERSON><PERSON> for sidebar updates.
 * Exports: pollDbAndGetData (async function)
 */

import sqlite3 from 'sqlite3';
import { open } from 'sqlite';

/**
 * Opens the SQLite3 DB and fetches the latest scraped data row.
 * @returns {Promise<object>} Latest data row as key-value pairs
 */
export async function pollDbAndGetData(dbPath: string): Promise<object> {
  const db = await open({ filename: dbPath, driver: sqlite3.Database });
  // Adjust table/column names as needed
  const row = await db.get('SELECT * FROM scraped_data ORDER BY updated_at DESC LIMIT 1');
  await db.close();
  return row || {};
}

// If you do not have 'sqlite' installed, run: npm install sqlite sqlite3
