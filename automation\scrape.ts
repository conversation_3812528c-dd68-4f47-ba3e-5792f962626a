/**
 * @file scrape.ts
 * @overview Utility to scrape all relevant game data from the main play page after login.
 * - Extracts all visible data from sidebars, center panel, movement controls, and buffs.
 * - Outputs a clean, structured object for further automation.
 */
import { Page } from 'playwright';
import * as fs from 'fs';
import * as path from 'path';
import dayjs from 'dayjs';
import { pollDbAndGetData } from '../utils/dbPoll';
import { initDb, insertScrapeData } from '../utils/dbWrite';
import { flattenToTwoLevels } from '../utils/format';
import { log } from '../utils/logger';

// Extend Window interface for TypeScript
declare global {
  interface Window {
    echoSidebarUpdate?: (data: any) => void;
  }
}

// Define types for auto-farm results
interface FarmResult {
  success: boolean;
  amount?: number;
  resourceType?: string;
  method?: string;
  error?: string;
}

export interface GameData {
  stats: Record<string, number | string>;
  battleLog: Record<string, number>;
  resourcePanel: Record<string, number>;
  inventory: {
    current: number;
    capacity: number;
  };  shrineBuffs: {
    spades: boolean;
    hearts: boolean;
    diamonds: boolean;
    clubs: boolean;
    totalBonus: number;
  };
  tile: {
    title: string;
    message: string;
    energy: number;
  };
  currentCoords: string;
  troopTransport: boolean;
  date: string;
  time: string;
}

export async function scrapeGameData(page: Page): Promise<GameData> {  // Fame stats (now called stats)
  const stats = await page.evaluate(() => {
    const fameBox = Array.from(document.querySelectorAll('.info-box .font-bold')).find(el => el.textContent?.trim() === 'Fame')?.closest('.info-box');
    if (!fameBox) return {};
    const stats: Record<string, number | string> = {};
    const parseNumber = (str: string): number => {
      const cleaned = str.replace(/[\,\s]/g, '');
      const num = parseInt(cleaned, 10);
      return isNaN(num) ? 0 : num;
    };
    const statRows = fameBox.querySelectorAll('.items-center');
    statRows.forEach(row => {
      let label = row.querySelector('div:not(.flex-1)')?.textContent?.trim() || '';
      const value = row.querySelector('.flex-1')?.textContent?.trim() || '';        if (label && value) {
        if (label === 'Power(B)') label = 'power';
        else if (label === 'Rank') label = 'rank';
        else if (label === 'Strength') label = 'strength';
        else if (label === 'Defense') label = 'defense';
        else if (label === 'Base') label = 'base';
        else if (label === 'Factories') label = 'factories';
        else if (label === 'Time') return; // Skip the Time field from stats
        else label = label.toLowerCase().replace(/[^a-z0-9]/g, '_');
        
        if (label !== 'inventory') {
          if (label === 'base') {
            // Keep base coordinates as a string
            stats[label] = value;
          } else {
            stats[label] = parseNumber(value);
          }
        }
      }
    });
    return stats;
  });
  // Battle Log (clean mapping with number conversion)
  const battleLog = await page.evaluate(() => {
    const logBox = Array.from(document.querySelectorAll('.info-box .font-bold')).find(el => el.textContent?.trim() === 'Battle Log')?.closest('.info-box');
    if (!logBox) return {};
    const logs: Record<string, number> = {};
    
    const parseNumber = (str: string): number => {
      const match = str.match(/(\d+)/);
      return match ? parseInt(match[1], 10) : 0;
    };
    
    logBox.querySelectorAll('.resource').forEach(div => {
      const textContent = div.textContent?.replace(/\s+/g, ' ').trim() || '';
      if (textContent.includes('Attack Logs')) logs['attack'] = parseNumber(textContent);
      else if (textContent.includes('Defense Logs')) logs['defense'] = parseNumber(textContent);
      else if (textContent.includes('Pike Logs')) logs['pike'] = parseNumber(textContent);
      else if (textContent.includes('Land Mines')) logs['land_mines'] = parseNumber(textContent);
    });
    
    return logs;
  });

  // Resource Panel (clean mapping with underscores and numbers)
  const resourcePanel = await page.evaluate(() => {
    const panelBox = Array.from(document.querySelectorAll('.info-box .font-bold')).find(el => el.textContent?.trim() === 'Resource Panel')?.closest('.info-box');
    if (!panelBox) return {};
    const resources: Record<string, number> = {};
    
    const parseNumber = (str: string): number => {
      const cleaned = str.replace(/[,\s]/g, '');
      const num = parseInt(cleaned, 10);
      return isNaN(num) ? 0 : num;
    };
    
    panelBox.querySelectorAll('.resource').forEach(div => {
      const textContent = div.textContent?.replace(/\s+/g, ' ').trim() || '';
      if (textContent.includes('Metal')) {
        const valueMatch = textContent.match(/Metal\s+([\d,]+)/);
        const bankedMatch = textContent.match(/Banked:\s*([\d,]+)/);
        if (valueMatch) resources['metal'] = parseNumber(valueMatch[1]);
        if (bankedMatch) resources['metal_banked'] = parseNumber(bankedMatch[1]);
      } else if (textContent.includes('Energy')) {
        const valueMatch = textContent.match(/Energy\s+([\d,]+)/);
        const bankedMatch = textContent.match(/Banked:\s*([\d,]+)/);
        if (valueMatch) resources['energy'] = parseNumber(valueMatch[1]);
        if (bankedMatch) resources['energy_banked'] = parseNumber(bankedMatch[1]);
      }
    });
    
    return resources;
  });  // Shrine Buffs (convert to boolean)
  const shrineBuffs = await page.evaluate(() => {
    const buffsBox = Array.from(document.querySelectorAll('.info-box .font-bold')).find(el => el.textContent?.trim() === 'Shrine Buffs')?.closest('.info-box');
    if (!buffsBox) return { spades: false, hearts: false, diamonds: false, clubs: false, totalBonus: 0 };
    const getBuff = (selector: string) => {
      const el = buffsBox.querySelector(selector)?.closest('.flex');
      if (!el) return false;
      return !el.textContent?.includes('No buff active');
    };
    const totalBonusMatch = buffsBox.textContent?.match(/Total Bonus\s*x\s*([\d.]+)/);
    const totalBonus = totalBonusMatch ? parseFloat(totalBonusMatch[1]) : 1.0;
    return {
      spades: getBuff('[name="suit-spade-fill"]'),
      hearts: getBuff('[name="suit-heart-fill"]'),
      diamonds: getBuff('[name="suit-diamond-fill"]'),
      clubs: getBuff('[name="suit-club-fill"]'),
      totalBonus
    };
  });

  // Troop Transport (detect if enabled)
  const troopTransport = await page.evaluate(() => {
    // Look for troop transport button or status
    const btn = Array.from(document.querySelectorAll('button')).find(b => b.textContent?.toLowerCase().includes('troop transport'));
    if (!btn) return false;
    // If button says 'Enable', it's off; if 'Disable', it's on
    return btn.textContent?.toLowerCase().includes('disable');
  });

  // Date/time (12h format, e.g., 6-23-2025, 01:23 pm)
  const now = dayjs();
  const date = now.format('M-D-YYYY');
  const time = now.format('hh:mm a');

  // Geothermal Vents (center panel with number conversion)
  const tile = await page.evaluate(() => {
    const title = document.querySelector('.bg-slate-700.text-xl')?.textContent?.replace(/\s+/g, ' ').trim() || '';
    // Message is in the .py-8.text-center div
    const message = document.querySelector('.py-8.text-center')?.textContent?.replace(/\s+/g, ' ').trim() || '';
    const energyMatch = message.match(/for ([\d,]+) Joules/);
    const energy = energyMatch ? parseInt(energyMatch[1].replace(/,/g, ''), 10) : 0;
    
    return {
      title,
      message,
      energy    };
  });

  // Current coordinates extraction - fix to get coordinates from top right corner
  const currentCoords = await page.evaluate(() => {
    // Method 1: Look for coordinates in the top right corner navigation area
    const topRightCoords = document.querySelector('[class*="P3X-"]')?.textContent;
    if (topRightCoords && topRightCoords.includes(',')) {
      const coordMatch = topRightCoords.match(/(\d+,\s*\d+)/);
      if (coordMatch) return coordMatch[1].trim();
    }
    
    // Method 2: Look in movement control area for coordinates
    const coordsEl = Array.from(document.querySelectorAll('.font-bold')).find(el => 
      el.textContent?.includes('P3X-')
    )?.querySelector('.text-right');
    if (coordsEl) {
      const coordText = coordsEl.textContent?.trim();
      if (coordText && coordText.includes(',')) {
        return coordText;
      }
    }
    
    // Method 3: Look for any element containing coordinate pattern (number, number)
    const allElements = document.querySelectorAll('*');
    for (const el of allElements) {
      const text = el.textContent || '';
      const coordMatch = text.match(/\b(\d{1,3},\s*\d{1,3})\b/);
      if (coordMatch && !text.includes('P3X-') && el.tagName !== 'SCRIPT') {
        // Verify it's likely coordinates (reasonable numbers)
        const [x, y] = coordMatch[1].split(',').map(n => parseInt(n.trim()));
        if (x > 0 && x < 999 && y > 0 && y < 999) {
          return coordMatch[1];
        }
      }
    }
    
    return '';
  });
  // Extract inventory separately with number conversion
  const inventory = await page.evaluate(() => {
    const fameBox = Array.from(document.querySelectorAll('.info-box .font-bold')).find(el => el.textContent?.trim() === 'Fame')?.closest('.info-box');
    if (!fameBox) return { current: 0, capacity: 0 };
    
    const inventoryRow = Array.from(fameBox.querySelectorAll('.items-center')).find(row => 
      row.textContent?.includes('Inventory')
    );
    
    if (inventoryRow) {
      const inventoryText = inventoryRow.querySelector('.flex-1')?.textContent?.trim() || '';
      const parts = inventoryText.split('/').map(s => s.trim());
      return {
        current: parseInt(parts[0] || '0', 10),
        capacity: parseInt(parts[1] || '0', 10)
      };
    }
      return { current: 0, capacity: 0 };
  });
  return {
    stats,
    battleLog,
    resourcePanel,
    inventory,
    shrineBuffs,
    tile,
    currentCoords,
    troopTransport: troopTransport ?? false,
    date,
    time
  };
}

/**
 * Save any object as pretty-printed JSON to the data directory and to SQLite DB.
 * Also triggers a direct sidebar update with the fresh data.
 * @param data The data object to save
 * @param filename The output filename (default: data.json)
 * @param page Optional Playwright page object to trigger sidebar update
 */
export async function saveGameData(data: any, filename = 'data.json', page?: Page) {
  const outPath = path.resolve(__dirname, '../data', filename);
  await fs.promises.writeFile(outPath, JSON.stringify(data, null, 2), 'utf-8');
  await initDb();
  await insertScrapeData(data);
  log.success(`Game data saved to ${outPath} and inserted into SQLite DB.`);
    // If page is provided, immediately update sidebar with fresh data
  if (page && !page.isClosed()) {
    try {
      await page.evaluate((freshData) => {
        // Silent direct sidebar update
        // @ts-ignore - window.echoSidebarUpdate is defined in sidebarOverlay.js
        if (typeof window !== 'undefined' && window.echoSidebarUpdate) {
          window.echoSidebarUpdate(freshData);
        }
      }, data);
    } catch (err) {
      log.error('Error in direct sidebar update:', err);
    }
  }
}

/**
 * Injects the sidebar overlay and wires up real-time data and controller actions.
 * Uses Playwright's page.exposeFunction and evaluate for communication.
 */
export async function injectSidebar(page: Page, dbPath: string): Promise<void> {
  log.debug('injectSidebar called with page and dbPath:', { hasPage: !!page, dbPath });
    // Expose controller action handler to the page
  await page.exposeFunction('echoSidebarAction', async (action: string) => {
    log.movement(`Controller action triggered: ${action}`);
    
    // Map controller actions to keyboard keys (3x3 grid: Q W E / A S D / Z X C)
    const keyMap: Record<string, string> = {
      'nw': 'q',      // Q = Northwest
      'n': 'w',       // W = North
      'ne': 'e',      // E = Northeast
      'w': 'a',       // A = West
      'center': 's',  // S = Center/Refresh
      'e': 'd',       // D = East
      'sw': 'z',      // Z = Southwest
      's': 'x',       // X = South
      'se': 'c'       // C = Southeast
    };
    
    const keyToPress = keyMap[action];
    if (keyToPress) {
      try {
        await page.keyboard.press(keyToPress);
        log.success(`Pressed key: ${keyToPress.toUpperCase()} for action: ${action}`);
      } catch (err) {
        log.error(`Failed to press key ${keyToPress}:`, err);
      }
    }
    
    return true;
  });  // Expose auto-farm function
  await page.exposeFunction('echoAutoFarm', async (resourceType: string): Promise<FarmResult> => {
    log.autofarm(`Auto-farm triggered for: ${resourceType}`);
    
    try {
      let farmKey = '';
      // Determine farm key based on resource type
      if (resourceType === 'energy') {
        farmKey = 'g';
      } else if (resourceType === 'metal') {
        farmKey = 'f'; 
      } else if (resourceType === 'cave') {
        farmKey = 'g'; // Caves also use 'g' key
      }
      
      if (!farmKey) {
        return { success: false, error: 'Unknown resource type' };
      }
      
      // Check for existing gather/collect button before farming
      const beforeFarmCheck = await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('input[type="button"], button'));
        const gatherButton = buttons.find(btn => {
          const value = (btn as HTMLInputElement).value?.toLowerCase() || '';
          const text = btn.textContent?.toLowerCase() || '';
          return value.includes('gather') || text.includes('gather') ||
                 value.includes('collect') || text.includes('collect');
        });
        return {
          hasGatherButton: !!gatherButton,
          buttonText: (gatherButton as HTMLInputElement)?.value || gatherButton?.textContent || ''
        };
      });
        // If there's already a gather button, click it directly instead of pressing G/F
      if (beforeFarmCheck.hasGatherButton) {
        log.autofarm('Found existing gather button, clicking it directly');
        
        await page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('input[type="button"], button'));
          const gatherButton = buttons.find(btn => {
            const value = (btn as HTMLInputElement).value?.toLowerCase() || '';
            const text = btn.textContent?.toLowerCase() || '';
            return value.includes('gather') || text.includes('gather') ||
                   value.includes('collect') || text.includes('collect');
          }) as HTMLElement;
          if (gatherButton) gatherButton.click();
        });
        
        // Wait briefly for the action to complete
        await page.waitForTimeout(200);
        
        // Check for success
        const afterGather = await page.evaluate(() => {
            const bodyText = document.body.textContent?.toLowerCase() || '';
            return {
              hasSuccess: bodyText.includes('collected') || bodyText.includes('gained') || bodyText.includes('found'),
              noGatherButton: !Array.from(document.querySelectorAll('input[type="button"], button')).some(btn => {
                const value = (btn as HTMLInputElement).value?.toLowerCase() || '';
                const text = btn.textContent?.toLowerCase() || '';
                return value.includes('gather') || text.includes('gather');
              })
            };
          });
        
        return { 
          success: afterGather.hasSuccess || afterGather.noGatherButton, 
          amount: 0, // Could parse from success message if needed
          resourceType: resourceType,
          method: 'direct_click'
        };
      }
      
      // Press the farm key
      await page.keyboard.press(farmKey);
      log.autofarm(`Pressed ${farmKey.toUpperCase()} to farm ${resourceType}`);
      
      // Wait for page to respond (reduced for faster response)
      await page.waitForTimeout(300);
      
      // Check for success using DOM evaluation instead of full content comparison
      const result = await page.evaluate(() => {
        const bodyText = document.body.textContent?.toLowerCase() || '';        const buttons = Array.from(document.querySelectorAll('input[type="button"], button'));
        const hasGatherButton = buttons.some(btn => {
          const value = (btn as HTMLInputElement).value?.toLowerCase() || '';
          const text = btn.textContent?.toLowerCase() || '';
          return value.includes('gather') || text.includes('gather');
        });
        
        const hasSuccessMessage = bodyText.includes('collected') || 
                                 bodyText.includes('gained') ||
                                 bodyText.includes('found') ||
                                 bodyText.includes('success');
        
        // Extract resource amount if possible
        const amountMatch = bodyText.match(/(\d+)\s*(metal|energy)/i);
        const amount = amountMatch ? parseInt(amountMatch[1], 10) : 0;
        
        return {
          hasGatherButton,
          hasSuccessMessage,
          amount
        };
      });
      
      const isSuccess = result.hasSuccessMessage || !result.hasGatherButton;
      
      log.autofarm(`Auto-farm result: success=${isSuccess}, amount=${result.amount}, hasGatherButton=${result.hasGatherButton}`);
      
      return { 
        success: isSuccess, 
        amount: result.amount,
        resourceType: resourceType,
        method: 'keypress'
      };
      
    } catch (err) {
      log.error(`Auto-farm error for ${resourceType}:`, err);
      return { success: false, error: (err as Error).message || 'Unknown error' };
    }
  });

  // Declare updateInterval variable
  let updateInterval: NodeJS.Timeout | null = null;
    // Expose data update function
  async function updateSidebar() {
    try {
      const rawData = await pollDbAndGetData(dbPath);
      // Silent data retrieval
      
      // Parse the JSON string from the database
      let data = rawData;
      if (rawData && typeof rawData === 'object' && 'data' in rawData && typeof rawData.data === 'string') {
        try {
          data = JSON.parse(rawData.data);
          // Silent parsing
        } catch (e) {
          log.error('Error parsing JSON from database:', e);
          data = rawData;
        }
      }

      await page.evaluate((d: any) => {
        // Silent sidebar update
        // @ts-ignore - window.echoSidebarUpdate is defined in sidebarOverlay.js
        if (typeof window !== 'undefined' && window.echoSidebarUpdate) {
          window.echoSidebarUpdate(d);
        }
      }, data);
    } catch (err) {
      log.error('Error updating sidebar:', err);
      if (updateInterval) {
        clearInterval(updateInterval);
        updateInterval = null;
      }
    }
  }

  // Inject the overlay script
  log.info('Injecting sidebar overlay...');
  await page.addScriptTag({ path: require('path').resolve(__dirname, 'sidebarOverlay.js') });
  log.success('Sidebar overlay script injected.');

  // Initial data update
  await updateSidebar();
  // Also provide a direct update function that can be called with fresh data
  await page.exposeFunction('echoDirectUpdate', async (freshData: any) => {
    await page.evaluate((d: any) => {
      // @ts-ignore - window.echoSidebarUpdate is defined in sidebarOverlay.js
      if (typeof window !== 'undefined' && window.echoSidebarUpdate) {
        window.echoSidebarUpdate(d);
      }
    }, freshData);
  });

  // Expose a function to re-scrape and update sidebar (called after movement)
  await page.exposeFunction('echoRescrapeAndUpdate', async () => {
    try {
      log.scrape('Re-scraping game data after movement...');
      const freshData = await scrapeGameData(page);
      const flatData = flattenToTwoLevels(freshData);
      await saveGameData(flatData, 'data.json', page); // This will auto-update sidebar
      log.success('Movement-triggered scrape completed.');
      return flatData;
    } catch (err) {
      console.log('Error in movement-triggered scrape:', err);
      return null;
    }
  });

  // Poll for DB changes every 500ms (adjust as needed)
  updateInterval = setInterval(updateSidebar, 500);
  
  // Clean up interval when page closes
  page.on('close', () => {
    if (updateInterval) {
      clearInterval(updateInterval);
      updateInterval = null;
    }
  });
}
