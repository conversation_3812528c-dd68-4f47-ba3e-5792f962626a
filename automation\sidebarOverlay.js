/*
 * OVERVIEW: Enhanced Sidebar Overlay UI for Game Automation
 * Injected by <PERSON><PERSON>. Uses Shadow DOM for isolation.
 * Features: Real-time data, 3x3 controller, auto-farm system, session tracking
 * Auto-farm: Detects energy/metal/cave nodes, presses G/F, logs results
 */

(function() {
  // Load Font Awesome 6
  const fontAwesome = document.createElement('link');
  fontAwesome.rel = 'stylesheet';
  fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css';
  document.head.appendChild(fontAwesome);

  // Create shadow root for isolation
  const sidebar = document.createElement('div');
  sidebar.id = 'echo-sidebar-root';
  sidebar.style.position = 'fixed';
  sidebar.style.top = '0';
  sidebar.style.right = '0';
  sidebar.style.width = '400px';
  sidebar.style.height = '100vh';
  sidebar.style.background = 'rgba(20,22,30,0.98)';
  sidebar.style.zIndex = '999999';
  sidebar.style.boxShadow = '-2px 0 12px rgba(0,0,0,0.5)';
  sidebar.style.display = 'flex';
  sidebar.style.flexDirection = 'column';
  sidebar.style.fontFamily = 'system-ui, sans-serif';
  sidebar.style.color = '#fff';
  sidebar.style.userSelect = 'none';
  sidebar.style.overflowY = 'auto';

  const shadow = sidebar.attachShadow({ mode: 'open' });

  // Sidebar HTML with professional dark theme
  shadow.innerHTML = `
    <style>
      .header { 
        font-size: 1.2em; 
        font-weight: bold; 
        padding: 12px; 
        border-bottom: 1px solid #444; 
        background: #2d2d2d; 
        text-align: center;
        color: #e0e0e0;
      }
      .controller { 
        display: grid; 
        grid-template-columns: repeat(3, 48px); 
        grid-template-rows: repeat(3, 48px); 
        gap: 4px; 
        justify-content: center; 
        margin: 10px; 
        padding: 10px;
        background: #2a2a2a;
        border-bottom: 1px solid #444;
        border-radius: 4px;
      }
      .ctrl-btn { 
        background: #3a3a3a; 
        border: 1px solid #555; 
        border-radius: 4px; 
        color: #ddd; 
        font-size: 1.1em; 
        cursor: pointer; 
        transition: all 0.2s; 
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .ctrl-btn:hover { 
        background: #4a4a4a; 
        border-color: #666; 
      }
      .ctrl-btn:active { 
        background: #555; 
        transform: scale(0.95);
      }
      .coords-display {
        background: #3a3a3a;
        color: #fff;
        text-align: center;
        padding: 8px;
        font-weight: bold;
        font-size: 1.0em;
        border-bottom: 1px solid #444;
      }
      .auto-farm-section {
        background: #2a2a2a;
        padding: 10px;
        border-bottom: 1px solid #444;
      }
      .auto-farm-toggle {
        background: #404040;
        border: 1px solid #555;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-weight: normal;
        width: 100%;
        margin-bottom: 6px;
        transition: background 0.2s;
      }
      .auto-farm-toggle:hover { background: #4a4a4a; }
      .auto-farm-toggle.active { background: #d32f2f; border-color: #d32f2f; }
      .farm-status { font-size: 0.8em; color: #bbb; text-align: center; }
      .session-stats {
        background: #262626;
        padding: 8px;
        border-bottom: 1px solid #444;
        font-size: 0.8em;
      }
      .session-row {
        display: flex;
        justify-content: space-between;
        margin: 2px 0;
        align-items: center;
      }
      .session-gains {
        background: #262626;
        padding: 8px;
        border-bottom: 1px solid #444;
        font-size: 0.8em;
      }
      .gain-row {
        display: flex;
        justify-content: space-between;
        margin: 2px 0;
        align-items: center;
      }
      .action-feed {
        background: #222;
        padding: 6px;
        max-height: 100px;
        overflow-y: auto;
        border-bottom: 1px solid #444;
        font-size: 0.7em;
      }
      .action-item {
        color: #ccc;
        margin: 1px 0;
        font-family: monospace;
        padding: 1px 0;
      }
      .data-tables {
        padding: 8px;
        max-height: 200px;
        overflow-y: auto;
        border-bottom: 1px solid #444;
        font-size: 0.75em;
      }
      .data-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 8px;
        background: #2a2a2a;
        border-radius: 3px;
        overflow: hidden;
      }
      .table-header {
        background: #333;
        color: #ddd;
        font-weight: bold;
        padding: 4px 6px;
        text-align: center;
        font-size: 0.85em;
      }
      .table-row {
        border-bottom: 1px solid #444;
      }
      .table-cell {
        padding: 3px 6px;
        border-right: 1px solid #444;
      }
      .table-cell:last-child { border-right: none; }
      .table-label { color: #aaa; }
      .table-value { color: #fff; font-weight: bold; text-align: right; }
      .status { 
        font-size: 0.8em; 
        color: #bbb; 
        padding: 8px; 
        text-align: center;
        background: #1a1a1a;
        border-top: 1px solid #444;
      }
      .icon { margin-right: 4px; color: #888; }
      .collapsible {
        cursor: pointer;
        user-select: none;
      }
      .collapsible:hover {
        background: #333;
      }
    </style>
    
    <div class="header">
      <i class="fas fa-cog icon"></i>ECHO Bot Control
    </div>
    
    <div class="controller" id="echo-controller">
      <button class="ctrl-btn" data-action="nw" title="Northwest (Q)">↖</button>
      <button class="ctrl-btn" data-action="n" title="North (W)">↑</button>
      <button class="ctrl-btn" data-action="ne" title="Northeast (E)">↗</button>
      <button class="ctrl-btn" data-action="w" title="West (A)">←</button>
      <button class="ctrl-btn" data-action="center" title="Center/Refresh (S)">⌂</button>
      <button class="ctrl-btn" data-action="e" title="East (D)">→</button>
      <button class="ctrl-btn" data-action="sw" title="Southwest (Z)">↙</button>
      <button class="ctrl-btn" data-action="s" title="South (X)">↓</button>
      <button class="ctrl-btn" data-action="se" title="Southeast (C)">↘</button>
    </div>
    
    <div class="coords-display" id="echo-coords">
      <i class="fas fa-map-marker-alt icon"></i>Loading coordinates...
    </div>
    
    <div class="auto-farm-section">
      <button class="auto-farm-toggle" id="auto-farm-toggle">
        <i class="fas fa-play icon"></i>Start Auto-Farm
      </button>
      <div class="farm-status" id="farm-status">
        <i class="fas fa-circle icon" style="color: #999;"></i>Auto-farm disabled
      </div>
    </div>
    
    <div class="session-stats" id="echo-session">
      <div class="table-header collapsible" onclick="toggleSection('session-content')">
        <i class="fas fa-clock icon"></i>Session Stats
        <i class="fas fa-chevron-down" style="float: right;"></i>
      </div>
      <div id="session-content">
        <div class="session-row">
          <span><i class="fas fa-stopwatch icon"></i>Duration:</span>
          <span id="session-duration">00:00</span>
        </div>
        <div class="session-row">
          <span><i class="fas fa-mouse icon"></i>Total Moves:</span>
          <span id="total-moves">0</span>
        </div>
        <div class="session-row">
          <span><i class="fas fa-tachometer-alt icon"></i>APM:</span>
          <span id="actions-per-min">0.0</span>
        </div>
        <div class="session-row">
          <span><i class="fas fa-hand-pointer icon"></i>Last Action:</span>
          <span id="last-action">None</span>
        </div>
      </div>
    </div>
    
    <div class="session-gains">
      <div class="table-header collapsible" onclick="toggleSection('gains-content')">
        <i class="fas fa-chart-line icon"></i>Session Gains
        <i class="fas fa-chevron-down" style="float: right;"></i>
      </div>
      <div id="gains-content">
        <div class="gain-row">
          <span><i class="fas fa-bolt icon" style="color: #ffeb3b;"></i>Energy:</span>
          <span id="energy-gained">+0</span>
        </div>
        <div class="gain-row">
          <span><i class="fas fa-hammer icon" style="color: #9e9e9e;"></i>Metal:</span>
          <span id="metal-gained">+0</span>
        </div>
        <div class="gain-row">
          <span><i class="fas fa-mountain icon" style="color: #8d6e63;"></i>Caves:</span>
          <span id="caves-explored">0</span>
        </div>
        <div class="gain-row">
          <span><i class="fas fa-seedling icon" style="color: #4caf50;"></i>Farm Actions:</span>
          <span id="farm-actions">0</span>
        </div>
      </div>
    </div>
    
    <div class="action-feed" id="echo-actions">
      <div class="table-header collapsible" onclick="toggleSection('actions-content')">
        <i class="fas fa-list icon"></i>Action Feed
        <i class="fas fa-chevron-down" style="float: right;"></i>
      </div>
      <div id="actions-content">
        <div style="color: #888; text-align: center; font-style: italic;">No actions yet</div>
      </div>
    </div>
    
    <div class="data-tables" id="echo-data-tables">
      <div class="table-header collapsible" onclick="toggleSection('data-content')">
        <i class="fas fa-database icon"></i>Game Data
        <i class="fas fa-chevron-down" style="float: right;"></i>
      </div>
      <div id="data-content">Loading...</div>
    </div>
    
    <div class="status" id="echo-status">
      <i class="fas fa-check-circle icon"></i>Ready
    </div>
  `;

  // Session tracking variables
  let sessionStartTime = Date.now();
  let totalMoves = 0;
  let actionHistory = [];
  let lastAction = 'None';
  let sessionGains = {
    energy: 0,
    metal: 0,
    caves: 0,
    farmActions: 0
  };
  let autoFarmEnabled = false;
  let currentTileType = '';

  // Global functions for collapsible sections
  window.toggleSection = function(sectionId) {
    const section = shadow.getElementById(sectionId);
    const chevron = section.previousElementSibling.querySelector('.fa-chevron-down, .fa-chevron-right');
    if (section.style.display === 'none') {
      section.style.display = 'block';
      chevron.className = 'fas fa-chevron-down';
    } else {
      section.style.display = 'none';
      chevron.className = 'fas fa-chevron-right';
    }
  };

  // Add to page
  document.body.appendChild(sidebar);

  // Auto-farm toggle
  const autoFarmToggle = shadow.getElementById('auto-farm-toggle');
  const farmStatus = shadow.getElementById('farm-status');
  
  autoFarmToggle.addEventListener('click', () => {
    autoFarmEnabled = !autoFarmEnabled;
    if (autoFarmEnabled) {
      autoFarmToggle.innerHTML = '<i class="fas fa-stop icon"></i>Stop Auto-Farm';
      autoFarmToggle.classList.add('active');
      farmStatus.innerHTML = '<i class="fas fa-circle icon" style="color: #4caf50;"></i>Auto-farm enabled';
      startAutoFarm();
    } else {
      autoFarmToggle.innerHTML = '<i class="fas fa-play icon"></i>Start Auto-Farm';
      autoFarmToggle.classList.remove('active');
      farmStatus.innerHTML = '<i class="fas fa-circle icon" style="color: #999;"></i>Auto-farm disabled';
    }
  });
  // Auto-farm logic
  function startAutoFarm() {
    if (!autoFarmEnabled) return;
    
    console.log('Auto-farm checking tile type:', currentTileType);
    console.log('Auto-farm enabled status:', autoFarmEnabled);
    
    // Check current tile type and trigger appropriate farming action
    if (currentTileType.includes('forest') || 
        currentTileType.includes('energy') || 
        currentTileType.includes('geothermal') ||
        currentTileType.includes('vents') ||
        currentTileType.includes('trees')) {
      console.log('Energy tile detected, triggering energy farm...');
      if (window.echoAutoFarm) {
        window.echoAutoFarm('energy').then(result => {
          console.log('Energy farm result:', result);
          if (result.success) {
            sessionGains.energy += result.amount || 0;
            sessionGains.farmActions++;
            logAction(`⚡ Farmed energy: +${result.amount || 'unknown'}`);
          } else {
            logAction(`❌ Energy farm failed: ${result.error || 'unknown'}`);
          }
          updateSessionGains();
        }).catch(err => {
          console.log('Energy farm error:', err);
          logAction(`❌ Energy farm error: ${err.message || 'unknown'}`);
        });
      } else {
        console.log('echoAutoFarm function not available');
      }
    } else if (currentTileType.includes('metal') || 
               currentTileType.includes('deposit') ||
               currentTileType.includes('mine') ||
               currentTileType.includes('ore')) {
      console.log('Metal tile detected, triggering metal farm...');
      if (window.echoAutoFarm) {
        window.echoAutoFarm('metal').then(result => {
          console.log('Metal farm result:', result);
          if (result.success) {
            sessionGains.metal += result.amount || 0;
            sessionGains.farmActions++;
            logAction(`🔨 Farmed metal: +${result.amount || 'unknown'}`);
          } else {
            logAction(`❌ Metal farm failed: ${result.error || 'unknown'}`);
          }
          updateSessionGains();
        }).catch(err => {
          console.log('Metal farm error:', err);
          logAction(`❌ Metal farm error: ${err.message || 'unknown'}`);
        });
      } else {
        console.log('echoAutoFarm function not available');
      }
    } else if (currentTileType.includes('cave') ||
               currentTileType.includes('cavern') ||
               currentTileType.includes('tunnel')) {
      console.log('Cave tile detected, triggering cave exploration...');
      if (window.echoAutoFarm) {
        window.echoAutoFarm('cave').then(result => {
          console.log('Cave exploration result:', result);
          if (result.success) {
            sessionGains.caves++;
            sessionGains.farmActions++;
            logAction(`⛰️ Explored cave`);
          } else {
            logAction(`❌ Cave exploration failed: ${result.error || 'unknown'}`);
          }
          updateSessionGains();
        }).catch(err => {
          console.log('Cave exploration error:', err);
          logAction(`❌ Cave exploration error: ${err.message || 'unknown'}`);
        });
      } else {
        console.log('echoAutoFarm function not available');
      }
    } else {
      console.log('No farmable resource detected on current tile. Tile type:', currentTileType);
    }
      // Continue auto-farm loop
    if (autoFarmEnabled) {
      setTimeout(startAutoFarm, 500); // Reduced to 500ms for much faster farming
    }
  }

  // Controller event handling
  const controller = shadow.getElementById('echo-controller');
  const status = shadow.getElementById('echo-status');
  controller.addEventListener('click', e => {
    if (e.target && e.target.matches('.ctrl-btn')) {
      const action = e.target.getAttribute('data-action');
      
      // Update session stats
      totalMoves++;
      lastAction = action.toUpperCase();
      const timestamp = new Date().toLocaleTimeString();
      
      logAction(`🎮 ${lastAction} movement`);
      updateSessionStats();
      
      status.innerHTML = `<i class="fas fa-spinner fa-spin icon"></i>Action: ${action}...`;
      
      // Call Playwright-exposed function for action
      if (window.echoSidebarAction) {
        window.echoSidebarAction(action).then(() => {
          status.innerHTML = `<i class="fas fa-check icon"></i>Action: ${action} sent`;
        }).catch(err => {
          status.innerHTML = `<i class="fas fa-exclamation-triangle icon"></i>Error: ${err.message}`;
        });
      } else {
        status.innerHTML = '<i class="fas fa-exclamation-triangle icon"></i>Controller bridge not available';
      }
    }
  });

  // Utility functions
  function logAction(actionText) {
    const timestamp = new Date().toLocaleTimeString();
    actionHistory.unshift(`${timestamp} - ${actionText}`);
    if (actionHistory.length > 15) actionHistory.pop();
    
    const actionFeed = shadow.getElementById('actions-content');
    actionFeed.innerHTML = actionHistory.map(item => 
      `<div class="action-item">${item}</div>`
    ).join('');
  }

  function updateSessionStats() {
    const duration = Math.floor((Date.now() - sessionStartTime) / 1000);
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    const durationStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    
    const apm = duration > 0 ? (totalMoves / (duration / 60)).toFixed(1) : '0.0';
    
    shadow.getElementById('session-duration').textContent = durationStr;
    shadow.getElementById('total-moves').textContent = totalMoves;
    shadow.getElementById('actions-per-min').textContent = apm;
    shadow.getElementById('last-action').textContent = lastAction;
  }

  function updateSessionGains() {
    shadow.getElementById('energy-gained').textContent = `+${sessionGains.energy}`;
    shadow.getElementById('metal-gained').textContent = `+${sessionGains.metal}`;
    shadow.getElementById('caves-explored').textContent = sessionGains.caves;
    shadow.getElementById('farm-actions').textContent = sessionGains.farmActions;
  }
  // Enhanced data update bridge with better coordinate handling
  window.echoSidebarUpdate = function(data) {
    const coordsDisplay = shadow.getElementById('echo-coords');
    const dataContent = shadow.getElementById('data-content');
    
    console.log('Sidebar update received:', data);
    console.log('Debug: data.stats:', data.stats);
    console.log('Debug: data.stats.base:', data.stats ? data.stats.base : 'No stats object');
    console.log('Debug: data.currentCoords:', data.currentCoords);
    
    if (!data) {
      coordsDisplay.innerHTML = '<i class="fas fa-map-marker-alt icon"></i>No data available';
      dataContent.innerHTML = '<div style="text-align: center; color: #888;">No data available</div>';
      return;
    }    // Update coordinates display - only show current location
    let coordsText = 'Unknown location';
    
    // Current coordinates (where you are now) - this is all we need to show
    if (data.currentCoords && data.currentCoords.trim()) {
      coordsText = data.currentCoords;
    } else if (data.current_coords && data.current_coords.trim()) {
      coordsText = data.current_coords;
    }
    
    coordsDisplay.innerHTML = `<i class="fas fa-map-marker-alt icon"></i>${coordsText}`;
      // Update current tile type for auto-farm with multiple checks
    if (data.tile && data.tile.title) {
      currentTileType = data.tile.title.toLowerCase();
    } else if (data.tile_title) {
      currentTileType = data.tile_title.toLowerCase();
    }
    
    // Also check the page title or any other indicators
    const pageTitle = document.title || '';
    const pageText = document.body.textContent || '';
    
    if (pageTitle.toLowerCase().includes('forest') || pageText.toLowerCase().includes('forest')) {
      currentTileType = 'forest';
    } else if (pageTitle.toLowerCase().includes('metal') || pageText.toLowerCase().includes('metal deposit')) {
      currentTileType = 'metal deposit';
    } else if (pageTitle.toLowerCase().includes('geothermal') || pageText.toLowerCase().includes('geothermal')) {
      currentTileType = 'geothermal vents';
    } else if (pageTitle.toLowerCase().includes('cave') || pageText.toLowerCase().includes('cave')) {
      currentTileType = 'cave';
    }
    
    console.log('Current tile type detected:', currentTileType);
    console.log('Raw tile data:', data.tile);
    
    // Create organized data tables
    let html = '';
    
    // Stats table
    const statsData = data.stats || data;
    if (statsData && (statsData.rank || statsData.power || statsData.strength)) {
      html += `
        <table class="data-table">
          <tr><td class="table-header" colspan="2"><i class="fas fa-user icon"></i>Player Stats</td></tr>
      `;
      
      ['rank', 'power', 'strength', 'defense', 'base', 'factories'].forEach(key => {
        const value = statsData[key];
        if (value !== undefined && value !== null) {
          html += `
            <tr class="table-row">
              <td class="table-cell table-label">${key}:</td>
              <td class="table-cell table-value">${typeof value === 'number' ? value.toLocaleString() : value}</td>
            </tr>
          `;
        }
      });
      
      html += `</table>`;
    }
    
    // Resources table
    const resourceData = data.resourcePanel || data.resource_panel || {};
    if (resourceData && (resourceData.metal !== undefined || resourceData.energy !== undefined)) {
      html += `
        <table class="data-table">
          <tr><td class="table-header" colspan="2"><i class="fas fa-coins icon"></i>Resources</td></tr>
      `;
      
      Object.entries(resourceData).forEach(([k, v]) => {
        if (v !== undefined && v !== null) {
          html += `
            <tr class="table-row">
              <td class="table-cell table-label">${k.replace(/_/g, ' ')}:</td>
              <td class="table-cell table-value">${typeof v === 'number' ? v.toLocaleString() : v}</td>
            </tr>
          `;
        }
      });
      
      html += `</table>`;
    }
    
    // Current tile table
    const tileData = data.tile || {};
    if (tileData.title || tileData.message) {
      html += `
        <table class="data-table">
          <tr><td class="table-header" colspan="2"><i class="fas fa-map icon"></i>Current Tile</td></tr>
          <tr class="table-row">
            <td class="table-cell table-label">Type:</td>
            <td class="table-cell table-value">${tileData.title || 'Unknown'}</td>
          </tr>
          <tr class="table-row">
            <td class="table-cell table-label">Energy:</td>
            <td class="table-cell table-value">${tileData.energy || 0}</td>
          </tr>
        </table>
      `;
    }
    
    // Inventory table
    const inventoryData = data.inventory || {};
    if (inventoryData.current !== undefined || inventoryData.capacity !== undefined) {
      html += `
        <table class="data-table">
          <tr><td class="table-header" colspan="2"><i class="fas fa-backpack icon"></i>Inventory</td></tr>
          <tr class="table-row">
            <td class="table-cell table-label">Items:</td>
            <td class="table-cell table-value">${inventoryData.current || 0}/${inventoryData.capacity || 0}</td>
          </tr>
        </table>
      `;
    }
    
    if (!html) {
      html = '<div style="text-align: center; color: #888; padding: 20px;">Waiting for game data...</div>';
    }
    
    dataContent.innerHTML = html;
  };
  // Update session stats every second
  setInterval(updateSessionStats, 1000);
  // Movement detection and auto-rescrape - INSTANT VERSION
  let lastKnownCoords = '';
  let lastKnownTile = '';
  
  // Function to detect movement and trigger rescrape
  function detectMovementAndRescrape() {
    // Check for coordinate changes in the page
    const currentPageText = document.body.textContent || '';
    const coordMatch = currentPageText.match(/(\d+,\s*\d+)/);
    const currentCoords = coordMatch ? coordMatch[1] : '';
    
    // Check for tile changes
    const tileMatch = currentPageText.match(/(METAL DEPOSIT|FOREST|CAVE|ENERGY|FACTORY)/i);
    const currentTile = tileMatch ? tileMatch[1] : '';
    
    // If coordinates or tile type changed, trigger rescrape
    if ((currentCoords && currentCoords !== lastKnownCoords) || 
        (currentTile && currentTile !== lastKnownTile)) {
      
      console.log(`Movement detected! Coords: ${lastKnownCoords} -> ${currentCoords}, Tile: ${lastKnownTile} -> ${currentTile}`);
      
      // Update our tracking variables
      lastKnownCoords = currentCoords;
      lastKnownTile = currentTile;
      
      // Trigger rescrape if function is available
      if (window.echoRescrapeAndUpdate) {
        console.log('Triggering instant rescrape after movement...');
        window.echoRescrapeAndUpdate().then(freshData => {
          if (freshData) {
            console.log('Instant rescrape completed with fresh data:', freshData);
          }
        }).catch(err => {
          console.log('Error in movement rescrape:', err);
        });
      }
    }
  }
  
  // INSTANT DETECTION: Check for movement every 100ms (10x faster)
  setInterval(detectMovementAndRescrape, 100);
  
  // REAL-TIME DOM MONITORING: Use MutationObserver for instant detection
  const observer = new MutationObserver((mutations) => {
    let shouldCheck = false;
    mutations.forEach((mutation) => {
      // Check if text content changed (coordinates/tiles update)
      if (mutation.type === 'childList' || mutation.type === 'characterData') {
        shouldCheck = true;
      }
    });
    
    if (shouldCheck) {
      // Debounce to avoid excessive calls
      clearTimeout(window.movementDebounce);
      window.movementDebounce = setTimeout(detectMovementAndRescrape, 50);
    }
  });
  
  // Start observing the document for changes
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    characterData: true
  });
  
  // CLICK DETECTION: Instant rescrape on any click (movement buttons)
  document.addEventListener('click', () => {
    setTimeout(detectMovementAndRescrape, 100); // Small delay to let page update
  });
  
  // KEYPRESS DETECTION: Instant rescrape on arrow keys or movement keys
  document.addEventListener('keydown', (e) => {
    // Check for movement keys (arrows, WASD, etc.)
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'w', 'a', 's', 'd'].includes(e.key)) {
      setTimeout(detectMovementAndRescrape, 100); // Small delay to let page update
    }
  });
  
  // URL CHANGE DETECTION: Instant detection
  let lastUrl = window.location.href;
  setInterval(() => {
    if (window.location.href !== lastUrl) {
      lastUrl = window.location.href;
      console.log('URL change detected, triggering instant rescrape...');
      if (window.echoRescrapeAndUpdate) {
        window.echoRescrapeAndUpdate();
      }
    }  }, 50); // Check URL every 50ms

})();

// Module export for TypeScript compatibility
module.exports = {};
