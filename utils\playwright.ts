// ...existing code...
// Utility for Playwright browser context creation (maximized, with storageState)
import { chromium, BrowserContext } from 'playwright';

export async function createMaximizedContext(storageState?: string): Promise<{ browser: any, context: BrowserContext }> {
  const browser = await chromium.launch({ headless: false, args: ['--start-maximized'] });
  const context = await browser.newContext({
    ...(storageState ? { storageState } : {}),
    viewport: null
  });
  return { browser, context };
}
// ...existing code...
