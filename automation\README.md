# sowclassic.com Playwright Automation Bot

## Overview
Automates login to https://sowclassic.com/game/play using <PERSON><PERSON> (TypeScript).
- Loads credentials from `.env`
- Uses Chromium only
- Persists session cookies to `cookies.json`
- Detects login state and avoids duplicate login
- Keeps browser open for further automation

## Setup

1. **Install dependencies:**
   ```sh
   npm install playwright dotenv typescript @types/node
   ```
2. **Create a `.env` file:**
   - Copy `.env` from the example and fill in your email and password.

3. **Run the bot:**
   ```sh
   npx ts-node automation/login.ts
   ```

## Environment Variables
- `EMAIL` - Your sowclassic.com account email
- `PASSWORD` - Your sowclassic.com account password

## Files
- `automation/login.ts` - Main automation script
- `automation/cookies.json` - Persistent session storage
- `.env` - Credentials (never commit this file)

## Notes
- The script will keep the browser open after login for further automation.
- If already logged in, it will not attempt to log in again.
- All actions and errors are logged to the console.
