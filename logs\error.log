{"level":"error","message":"Error updating sidebar: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at updateSidebar (D:\\BRAIN\\automation\\scrape.ts:456:18)\n    at injectSidebar (D:\\BRAIN\\automation\\scrape.ts:476:3)\n    at main (D:\\BRAIN\\automation\\login.ts:63:5)","timestamp":"07:20:28"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at main (D:\\BRAIN\\automation\\login.ts:66:5)","timestamp":"07:20:28"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:491:7","timestamp":"07:20:28"}
{"level":"error","message":"Error updating sidebar: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at Timeout.updateSidebar (D:\\BRAIN\\automation\\scrape.ts:456:18)","timestamp":"07:20:29"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:491:7","timestamp":"07:20:53"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at main (D:\\BRAIN\\automation\\login.ts:66:5)","timestamp":"07:23:12"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:12"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:24"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:25"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:29"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:40"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:42"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:44"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:45"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:46"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:52"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:53"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:56"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:57"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:58"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:23:59"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:24:00"}
{"level":"error","message":"Error in direct sidebar update: page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)","name":"Error","stack":"page.evaluate: ReferenceError: logger_1 is not defined\n    at eval (eval at evaluate (:291:30), <anonymous>:2:17)\n    at UtilityScript.evaluate (<anonymous>:293:16)\n    at UtilityScript.<anonymous> (<anonymous>:1:44)\n    at saveGameData (D:\\BRAIN\\automation\\scrape.ts:269:18)\n    at D:\\BRAIN\\automation\\scrape.ts:490:7","timestamp":"07:24:06"}
{"level":"error","message":"Auto-farm error for metal: page.waitForTimeout: Target page, context or browser has been closed","stack":"page.waitForTimeout: Target page, context or browser has been closed\n    at D:\\BRAIN\\automation\\scrape.ts:390:18","timestamp":"07:28:56"}
{"level":"error","message":"Error updating sidebar: page.evaluate: Target page, context or browser has been closed","stack":"page.evaluate: Target page, context or browser has been closed\n    at Timeout.updateSidebar (D:\\BRAIN\\automation\\scrape.ts:454:18)","timestamp":"13:12:35"}
{"level":"error","message":"Auto-farm error for energy: page.waitForTimeout: Target page, context or browser has been closed","stack":"page.waitForTimeout: Target page, context or browser has been closed\n    at D:\\BRAIN\\automation\\scrape.ts:399:18","timestamp":"14:44:07"}
{"level":"error","message":"Auto-farm error for energy: keyboard.press: Target page, context or browser has been closed","stack":"keyboard.press: Target page, context or browser has been closed\n    at D:\\BRAIN\\automation\\scrape.ts:394:27","timestamp":"14:52:54"}
