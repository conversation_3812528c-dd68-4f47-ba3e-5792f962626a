/**
 * @file farm.ts
 * @overview Auto-farming utility for resource gathering
 * Handles instant farming of energy, metal, and caves with no delays
 */
import { Page } from 'playwright';
import { log } from './logger';

export interface FarmResult {
  success: boolean;
  resourceType: string;
  amount: number;
  error?: string;
  hasGatherButton: boolean;
}

/**
 * Detect resource type from tile title
 */
export function detectResourceType(tileTitle: string): string | null {
  const title = tileTitle.toLowerCase();
  
  // Energy sources
  if (title.includes('geothermal') || title.includes('energy')) {
    return 'energy';
  }
  
  // Metal sources
  if (title.includes('metal') || title.includes('deposit')) {
    return 'metal';
  }
  
  // Cave sources
  if (title.includes('cave')) {
    return 'cave';
  }
  
  return null;
}

/**
 * Get the correct farming key for resource type
 */
export function getFarmingKey(resourceType: string): string {
  switch (resourceType) {
    case 'energy':
      return 'g';
    case 'cave':
      return 'f';
    case 'metal':
      return 'g';
    default:
      return 'g';
  }
}

/**
 * Check if farming was successful by looking for success/failure messages
 */
export async function checkFarmingSuccess(page: Page): Promise<{ success: boolean; amount: number }> {
  try {
    // Look for success indicators in the tile message or page content
    const tileMessage = await page.$eval('.main_msg', el => el.textContent || '').catch(() => '');
    
    // Check for success messages
    if (tileMessage.includes('Eureka') || tileMessage.includes('found') || /\d+\s+(Joules|Metal|Energy)/i.test(tileMessage)) {
      // Extract amount from message if possible
      const amountMatch = tileMessage.match(/(\d+)\s+(?:Joules|Metal|Energy)/i);
      const amount = amountMatch ? parseInt(amountMatch[1]) : 100;
      return { success: true, amount };
    }
    
    // Check for failure messages
    if (tileMessage.includes('Bummer') || tileMessage.includes('empty') || tileMessage.includes('nothing')) {
      return { success: true, amount: 0 }; // Successful attempt, just no resources
    }
    
    // Default to successful with unknown amount
    return { success: true, amount: 0 };
  } catch (err) {
    log.error('Error checking farming success:', err);
    return { success: false, amount: 0 };
  }
}

/**
 * Instantly farm a resource with no delays
 */
export async function farmResource(page: Page, resourceType: string): Promise<FarmResult> {
  try {
    log.autofarm(`Auto-farm triggered for: ${resourceType}`);
    
    // Check if there's already a gather button
    const gatherButton = await page.$('input[value="Gather"]').catch(() => null);
    if (gatherButton) {
      log.autofarm('Found existing gather button, clicking it directly');
      await gatherButton.click();
      
      // Check result immediately
      const result = await checkFarmingSuccess(page);
      return {
        success: result.success,
        resourceType,
        amount: result.amount,
        hasGatherButton: true
      };
    }
    
    // Get the correct farming key
    const farmKey = getFarmingKey(resourceType);
    
    // Press the farming key instantly (no delays)
    await page.keyboard.press(farmKey);
    log.autofarm(`Pressed ${farmKey.toUpperCase()} to farm ${resourceType}`);
    
    // Check result immediately
    const result = await checkFarmingSuccess(page);
    
    log.autofarm(`Auto-farm result: success=${result.success}, amount=${result.amount}, hasGatherButton=false`);
    
    return {
      success: result.success,
      resourceType,
      amount: result.amount,
      hasGatherButton: false
    };
    
  } catch (err) {
    log.error(`Auto-farm error for ${resourceType}:`, err);
    return { 
      success: false, 
      resourceType,
      amount: 0,
      error: (err as Error).message || 'Unknown error',
      hasGatherButton: false
    };
  }
}

/**
 * Auto-farm based on current tile type
 */
export async function autoFarm(page: Page, tileTitle: string): Promise<FarmResult | null> {
  const resourceType = detectResourceType(tileTitle);
  
  if (!resourceType) {
    return null; // Not a farmable tile
  }
  
  return await farmResource(page, resourceType);
}
